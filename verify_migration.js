/**
 * Verification script to check the Arabic diacritics migration
 * This script will verify that all records have the arNoDiacritics fields
 */

require('@babel/register')({
  extends: './.babelrc'
})
require('babel-polyfill')
require('dotenv-flow').config()

import * as db from './src/common/db'
import Ayah from './src/packages/ayah/model'
import Surah from './src/packages/surah/model'

const verifyAyahs = async () => {
  try {
    console.log('🔍 Verifying Ayahs migration...')
    
    const totalAyahs = await Ayah.countDocuments({})
    console.log(`📊 Total ayahs in database: ${totalAyahs}`)
    
    // Check ayahs with missing arNoDiacritics fields
    const missingTitleNoDiacritics = await Ayah.countDocuments({
      'title.ar': { $exists: true },
      'title.arNoDiacritics': { $exists: false }
    })
    
    const missingContentNoDiacritics = await Ayah.countDocuments({
      'content.ar': { $exists: true },
      'content.arNoDiacritics': { $exists: false }
    })
    
    const missingExplanationNoDiacritics = await Ayah.countDocuments({
      'explanation.ar': { $exists: true },
      'explanation.arNoDiacritics': { $exists: false }
    })
    
    console.log(`   - Missing title.arNoDiacritics: ${missingTitleNoDiacritics}`)
    console.log(`   - Missing content.arNoDiacritics: ${missingContentNoDiacritics}`)
    console.log(`   - Missing explanation.arNoDiacritics: ${missingExplanationNoDiacritics}`)
    
    // Show some examples
    const sampleAyahs = await Ayah.find({}).limit(3)
    console.log('\n📝 Sample ayahs:')
    sampleAyahs.forEach((ayah, index) => {
      console.log(`\nAyah ${index + 1} (ID: ${ayah.id}):`)
      if (ayah.title) {
        console.log(`   Title AR: ${ayah.title.ar || 'N/A'}`)
        console.log(`   Title No Diacritics: ${ayah.title.arNoDiacritics || 'N/A'}`)
      }
      if (ayah.content) {
        console.log(`   Content AR: ${ayah.content.ar ? ayah.content.ar.substring(0, 50) + '...' : 'N/A'}`)
        console.log(`   Content No Diacritics: ${ayah.content.arNoDiacritics ? ayah.content.arNoDiacritics.substring(0, 50) + '...' : 'N/A'}`)
      }
    })
    
    const ayahsSuccess = missingTitleNoDiacritics === 0 && 
                        missingContentNoDiacritics === 0 && 
                        missingExplanationNoDiacritics === 0
    
    return ayahsSuccess
    
  } catch (error) {
    console.error('❌ Error verifying ayahs:', error)
    return false
  }
}

const verifySurahs = async () => {
  try {
    console.log('\n🔍 Verifying Surahs migration...')
    
    const totalSurahs = await Surah.countDocuments({})
    console.log(`📊 Total surahs in database: ${totalSurahs}`)
    
    // Check surahs with missing arNoDiacritics fields
    const missingTitleNoDiacritics = await Surah.countDocuments({
      'title.ar': { $exists: true },
      'title.arNoDiacritics': { $exists: false }
    })
    
    console.log(`   - Missing title.arNoDiacritics: ${missingTitleNoDiacritics}`)
    
    // Show some examples
    const sampleSurahs = await Surah.find({}).limit(5)
    console.log('\n📝 Sample surahs:')
    sampleSurahs.forEach((surah, index) => {
      console.log(`\nSurah ${index + 1} (ID: ${surah.id}):`)
      if (surah.title) {
        console.log(`   Title AR: ${surah.title.ar || 'N/A'}`)
        console.log(`   Title No Diacritics: ${surah.title.arNoDiacritics || 'N/A'}`)
        console.log(`   Title SW: ${surah.title.sw || 'N/A'}`)
      }
    })
    
    const surahsSuccess = missingTitleNoDiacritics === 0
    
    return surahsSuccess
    
  } catch (error) {
    console.error('❌ Error verifying surahs:', error)
    return false
  }
}

const runVerification = async () => {
  try {
    console.log('🔍 Starting Migration Verification')
    console.log('==================================')
    
    // Connect to database
    console.log('🔌 Connecting to database...')
    await db.connect()
    console.log('✅ Database connected successfully')
    
    // Run verifications
    const ayahsSuccess = await verifyAyahs()
    const surahsSuccess = await verifySurahs()
    
    console.log('\n📋 VERIFICATION SUMMARY')
    console.log('======================')
    console.log(`Ayahs Migration: ${ayahsSuccess ? '✅ SUCCESS' : '❌ FAILED'}`)
    console.log(`Surahs Migration: ${surahsSuccess ? '✅ SUCCESS' : '❌ FAILED'}`)
    
    if (ayahsSuccess && surahsSuccess) {
      console.log('\n🎉 All migrations verified successfully!')
      console.log('Your database is ready for Arabic diacritics-free search!')
    } else {
      console.log('\n⚠️  Some migrations need attention. Please run the migration script again.')
    }
    
  } catch (error) {
    console.error('\n💥 Verification failed:', error)
    process.exit(1)
  } finally {
    // Close database connection
    console.log('\n🔌 Closing database connection...')
    process.exit(0)
  }
}

// Run the verification
runVerification()
