// Debug script to analyze the Arabic characters
const { removeDiacritics } = require('./src/common/arabic-utils');

const text = "إِنَّآ أَعْطَيْنَـٰكَ ٱلْكَوْثَرَ";
console.log("Original text:", text);
console.log("Character by character analysis:");

for (let i = 0; i < text.length; i++) {
  const char = text[i];
  const code = char.charCodeAt(0);
  const hex = code.toString(16).toUpperCase().padStart(4, '0');
  console.log(`${i}: "${char}" -> U+${hex} (${code})`);
}

console.log("\nAfter removeDiacritics:");
const result = removeDiacritics(text);
console.log("Result:", result);

console.log("\nExpected: انا اعطيناك الكوثر");
console.log("Character by character analysis of expected:");
const expected = "انا اعطيناك الكوثر";
for (let i = 0; i < expected.length; i++) {
  const char = expected[i];
  const code = char.charCodeAt(0);
  const hex = code.toString(16).toUpperCase().padStart(4, '0');
  console.log(`${i}: "${char}" -> U+${hex} (${code})`);
}
