import { <PERSON>ronJob } from 'cron'
import SystemServices from './system'

if (process.env.NODE_ENV === "production") {
  // const every30thMinute = '30 * * * *'
  // const startJob = new CronJob({
  //   cronTime: every30thMinute,
  //   onTick: async () => {
  //     try {
  //       await SystemServices.backupDB()
  //     } catch (error) {
  //       console.log(error)
  //     }
  //   },
  //   timeZone: 'UTC',
  //   runOnInit: true
  // })

  // startJob.start()
}