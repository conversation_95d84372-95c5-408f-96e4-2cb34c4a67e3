import { uploadDatabase, deleteDatabaseFile } from '../common/file_utils'
import moment from 'moment'
const fs = require('fs')
import _ from 'lodash'
const spawn = require('child_process').spawn

function backupDB() {
  const fileName = moment().format('DD_MM_YYYY_HH_mm') + '.gz'
  const filePath = '/tmp/' + fileName
  let backupProcess = spawn('mongodump', [
    '--uri=' + process.env.MONGODB_URL,
    '--archive=' + filePath,
    '--gzip'
  ])
  console.log('--uri=' + process.env.MONGODB_URL + ' ' + '--archive=' + filePath + ' ' + '--gzip')
  return new Promise((resolve, reject) => {
    backupProcess.on('exit', (code, signal) => {
      if (code) {
        reject('Backup process exited with code ' + code)
      } else if (signal) {
        reject('Backup process was killed with singal ' + signal)
      } else {
        try {
          uploadDatabase(fileName, filePath)
            .then(() => {
              fs.unlinkSync(filePath)
              const oldFile = moment().subtract(7, 'days').format('DD_MM_YYYY') + '.gz'
              deleteDatabaseFile(oldFile).then(resolve).catch(reject)
            })
            .catch(reject)
        } catch (error) {
          reject(error)
        }
      }
    })
  })
}

export default {
  backupDB
}
