var express = require('express')
var path = require('path')
var cookieParser = require('cookie-parser')
import multiCores from './multi-cores'
import { EventEmitter } from 'events'
import * as db from './common/db'
var cors = require('cors')

require('dotenv-flow').config()
var i18n = require('./common/i18n')
import Constants from './common/constants'
var { response } = require('./common/utils')
import SurahServices from './packages/surah/service'
import AyahServices from './packages/ayah/service'

const mediator = new EventEmitter()
var app = express()

app.use(cors())

//multi languages
app.use(i18n.init)

// view engine setup
app.set('views', path.join(__dirname, 'views'))
app.set('view engine', 'pug')

app.use(express.json())
app.use(express.urlencoded({ extended: false }))
app.use(cookieParser())
app.use(express.static(path.join(__dirname, 'public')))
app.use(express.static(path.join(__dirname, 'apidoc')))
//if (process.env.NODE_ENV !== "production") {
app.use(express.static(process.cwd() + '/apidoc'))
app.get('/apidoc', (req, res) => {
  res.sendFile(path.join(process.cwd() + '/apidoc/index.html'))
})
//}

app.use(function (req, res, next) {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, PATCH')
  res.header(
    'Access-Control-Allow-Headers',
    'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma'
  )
  //res.header("Access-Control-Allow-Origin", "*");
  next()
})

multiCores(app, mediator)

mediator.once('boot.ready', async (server) => {
  console.log('SERVER BOOT READY: ', server.address())

  db.connect()

  //import data
  SurahServices.importData()
    .then(() => {
      console.log('Import Surah List Done')
      AyahServices.importData()
        .then(() => {
          console.log('Import Ayah List Done')
        })
        .catch((e) => {
          console.log(`Import Ayah List ${e}`)
        })
    })
    .catch((e) => {
      console.log(`Import Surah List ${e}`)
    })

  app.get('*', (req, res, next) => {
    console.log(req.url)
    if (req.query && req.query.page && req.query.page == 0) {
      req.query.page = 1
    }
    next()
  })

  app.use(require('./routers'))

  // catch 404 and forward to error handler
  app.use(function (req, res, next) {
    return res.status(404).jsonp(response(false, {}, 'Api not found'))
  })

  // error handler
  app.use(function (err, req, res, next) {
    console.log(err)
    if (err.message) {
      return res.status(500).jsonp(response(false, {}, err.message))
    } else {
      return res.status(500).send(err)
    }
  })
})

module.exports = app
