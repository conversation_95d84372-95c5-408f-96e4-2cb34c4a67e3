var path = require("path");
var express = require("express"),
  router = express.Router();

router.use("*", require("./middlewares/authorization").default);

router.use("/auth", require("./packages/auth/router"));
router.use("/files", require("./packages/file/router"));
router.use("/users", require("./packages/user/router"));
router.use("/surahs", require("./packages/surah/router"));
router.use("/ayahs", require("./packages/ayah/router"));
router.use("/juzus", require("./packages/juzu/router"));
router.use("/books", require("./packages/book/router"));

module.exports = router;
