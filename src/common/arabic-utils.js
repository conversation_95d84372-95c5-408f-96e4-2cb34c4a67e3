/**
 * Arabic text utilities for processing Arabic content
 */

/**
 * Remove Arabic diacritics (tashkeel) from text and normalize Arabic letters
 * This includes: fatha, damma, kasra, sukun, shadda, tan<PERSON>en, etc.
 * Also normalizes different forms of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>h <PERSON>
 * @param {string} text - Arabic text with diacritics
 * @returns {string} - Arabic text without diacritics and normalized
 */
export const removeDiacritics = (text) => {
  if (!text || typeof text !== 'string') {
    return text
  }

  // Handle special cases first
  // Convert Tatweel + Superscript Alef to regular Alef
  let normalized = text.replace(/\u0640\u0670/g, 'ا')

  // Convert standalone Superscript Alef to regular Alef
  normalized = normalized.replace(/\u0670/g, 'ا')

  // Remove all other diacritical marks
  // Arabic diacritics Unicode ranges:
  // U+064B to U+065F: Arabic diacritical marks (excluding U+0670 which we handled above)
  // U+06D6 to U+06ED: Additional Arabic marks
  // U+08D4 to U+08E1: Arabic tone marks
  // U+08E3 to U+08FF: Additional Arabic marks
  // U+0640: Arabic Tatweel (elongation character)
  const diacriticsRegex = /[\u064B-\u065F\u06D6-\u06ED\u08D4-\u08E1\u08E3-\u08FF\u0640]/g
  normalized = normalized.replace(diacriticsRegex, '')

  // Normalize different forms of Alef
  // أ (U+0623: Alef with Hamza above) -> ا (U+0627: Alef)
  // إ (U+0625: Alef with Hamza below) -> ا (U+0627: Alef)
  // آ (U+0622: Alef with Madda above) -> ا (U+0627: Alef)
  // ٱ (U+0671: Alef Wasla) -> ا (U+0627: Alef)
  normalized = normalized.replace(/[أإآٱ]/g, 'ا')

  // Normalize Yeh
  // ي (U+064A: Yeh) and ى (U+0649: Alef Maksura) -> ي
  normalized = normalized.replace(/ى/g, 'ي')

  // Don't normalize Teh Marbuta in this function - keep it as is for better search
  // The normalizeArabicText function can handle this if needed
  // normalized = normalized.replace(/ة/g, 'ه')

  return normalized.trim()
}

/**
 * Normalize Arabic text for better search
 * - Removes diacritics
 * - Normalizes different forms of alef
 * - Normalizes yeh and teh marbuta
 * @param {string} text - Arabic text to normalize
 * @returns {string} - Normalized Arabic text
 */
export const normalizeArabicText = (text) => {
  if (!text || typeof text !== 'string') {
    return text
  }

  let normalized = removeDiacritics(text)

  // Normalize different forms of Alef
  // أ (Alef with Hamza above) -> ا (Alef)
  // إ (Alef with Hamza below) -> ا (Alef)
  // آ (Alef with Madda above) -> ا (Alef)
  normalized = normalized.replace(/[أإآ]/g, 'ا')

  // Normalize Yeh
  // ي (Yeh) and ى (Alef Maksura) -> ي
  normalized = normalized.replace(/ى/g, 'ي')

  // Normalize Teh Marbuta
  // ة (Teh Marbuta) -> ه (Heh)
  normalized = normalized.replace(/ة/g, 'ه')

  return normalized.trim()
}

/**
 * Check if text contains Arabic characters
 * @param {string} text - Text to check
 * @returns {boolean} - True if text contains Arabic characters
 */
export const containsArabic = (text) => {
  if (!text || typeof text !== 'string') {
    return false
  }

  // Arabic Unicode range: U+0600 to U+06FF
  const arabicRegex = /[\u0600-\u06FF]/
  return arabicRegex.test(text)
}

/**
 * Extract Arabic words from text (removes punctuation and numbers)
 * @param {string} text - Arabic text
 * @returns {string[]} - Array of Arabic words
 */
export const extractArabicWords = (text) => {
  if (!text || typeof text !== 'string') {
    return []
  }

  // Match Arabic words (including diacritics)
  const arabicWordRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+/g
  const matches = text.match(arabicWordRegex)

  return matches || []
}

export default {
  removeDiacritics,
  normalizeArabicText,
  containsArabic,
  extractArabicWords
}
