var url = require("url");
var path = require("path");
import _ from "lodash";

import nodemailer from "nodemailer";
import Config from "./config.js";

const transporter = nodemailer.createTransport({
  host: Config.SMTP.host,
  port: Config.SMTP.port,
  auth: {
    user: Config.SMTP.username,
    pass: Config.SMTP.password,
  },
});

export const sendEmail = async (to, subject, html) => {
  try {
    await transporter.sendMail({
      from: Config.SMTP.username,
      to,
      subject,
      html,
    });
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const sendEmailWithFile = async (to, subject, html, attachments) => {
  try {
    const result = await transporter.sendMail({
      from: Config.SMTP.username,
      to,
      subject,
      html,
      attachments
    });
    console.log(result);
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const response = (success = true, data = {}, message = "") => {
  if (success) {
    return {
      success,
      data,
    };
  } else {
    return {
      success,
      message,
    };
  }
};

export const randomFileNameByExtension = (extension) => {
  return Math.random().toString(36).substring(7) + extension;
};

export const getImageUrl = (item) => {
  if (
    item &&
    item != "" &&
    item.indexOf("http") == -1
  ) {
    return getFileUrl(item);
  }
  return item;
};

export const getAudioFileUrl = (fileName) => {
  return `https://${Config.Spaces.bucket}.${Config.Spaces.endpoint}/audios/${fileName}`;
};

export const getFileUrl = (fileName) => {
  return `https://${Config.Spaces.bucket}.${Config.Spaces.endpoint}/images/${fileName}`;
};

export const getAvatarUrl = (avatar) => {
  if (
    avatar &&
    avatar != "" &&
    avatar.indexOf("http") == -1 &&
    avatar.indexOf("https") == -1
  ) {
    return getFileUrl(avatar);
  }
  if (avatar == null || avatar == undefined || avatar.length == 0) {
    return "https://qurani-tukufu.nyc3.cdn.digitaloceanspaces.com/images/default_avatar.jpg";
  }

  return avatar;
};

export const isDate = (date) => {
  return new Date(date) !== "Invalid Date" && !isNaN(new Date(date));
};

export const isPhone = (phone) => {
  return /^[0-9]+$/.test(phone);
};

export const decoratePhone = (phone) => {
  phone = phone.replace(/ /g, '')
  return phone.substring(0, 1) != '0' ? '0' + phone : phone;
};

export const asyncForEach = async (array, callback) => {
  for (let index = 0; index < array.length; index++) {
    await callback(array[index], index);
  }
};

