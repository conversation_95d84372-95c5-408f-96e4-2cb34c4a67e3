import { randomFileNameByExtension } from "./utils";
var path = require("path");
var fs = require("fs");
const { compress } = require("compress-images/promise");
const Jimp = require("jimp");

import Config from "./config";

const S3 = require("aws-sdk/clients/s3");
const AWS = require("aws-sdk");
const wasabiEndpoint = new AWS.Endpoint(Config.Spaces.endpoint);

const bucket = Config.Spaces.bucket;
const s3 = new S3({
  endpoint: wasabiEndpoint,
  region: Config.Spaces.region,
  accessKeyId: Config.Spaces.accessKeyId,
  secretAccessKey: Config.Spaces.secretAccessKey,
});

const uploadImage = (filePath, newPath) => {
  return new Promise((resolve, reject) => {
    const fileContent = fs.readFileSync(filePath);
    s3.putObject(
      {
        Body: fileContent,
        Bucket: bucket,
        Key: newPath,
        ACL: "public-read",
        ContentType: "image/jpeg",
      },
      (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      }
    );
  });
};

const uploadAudio = (filePath, newPath) => {
  return new Promise((resolve, reject) => {
    const fileContent = fs.readFileSync(filePath);
    s3.putObject(
      {
        Body: fileContent,
        Bucket: bucket,
        Key: newPath,
        ACL: "public-read",
        ContentType: "audio/mpeg",
      },
      (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      }
    );
  });
};

const convertImageToJpeg = async (fileName, filePath) => {
  return new Promise((resolve, reject) => {
    Jimp.read(filePath, function (err, image) {
      if (err) {
        reject(err);
      } else {
        const newFileName = path.parse(fileName).name + ".jpeg";
        const newPath = filePath.replace(fileName, newFileName);
        image.write(newPath);
        fs.unlinkSync(filePath);
        resolve(newPath);
      }
    });
  });
};

const decreaseImageSize = async (filePath, fileName) => {
  try {
    const result = await compress({
      source: filePath,
      destination: filePath,
      enginesSetup: {
        jpg: { engine: "mozjpeg", command: ["-quality", "70"] },
        png: { engine: "pngquant", command: ["--quality=20-50", "-o"] },
      },
    });

    const { statistics, errors } = result;
    if (errors.length > 0) {
      throw errors[0];
    } else {
      fs.renameSync(statistics[0]["path_out_new"], filePath);
    }
  } catch (error) {
    throw error;
  }
};

const moveFileToMainFolder = (fileName, filePath) => {
  return new Promise(async (resolve, reject) => {
    try {
      //await decreaseImageSize(filePath, fileName);
      //const newFileName = path.parse(fileName).name + ".jpeg";
      const newPath = "images/" + fileName;
      await uploadImage(filePath, newPath);
      resolve(fileName);
    } catch (error) {
      reject(error);
    }
  });
};

export const executeFile = async (fileName, filePath) => {
  try {
    //let newPath = await convertImageToJpeg(fileName, filePath);
    return await moveFileToMainFolder(fileName, filePath);
  } catch (error) {
    throw error;
  }
};

export const executeAudioFile = async (fileName, filePath) => {
  return new Promise(async (resolve, reject) => {
    try {
      const newPath = "audios/" + fileName;
      await uploadAudio(filePath, newPath);
      resolve(fileName);
    } catch (error) {
      reject(error);
    }
  });
};

export const deleteDatabaseFile = (fileName) => {
  return new Promise(async (resolve, reject) => {
    const newPath = 'db_backup/' + fileName
    s3.deleteObject(
      {
        Bucket: bucket,
        Key: newPath
      },
      (err, data) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      }
    )
  })
}

export const uploadDatabase = (fileName, filePath) => {
  return new Promise(async (resolve, reject) => {
    const newPath = 'db_backup/' + fileName
    const fileContent = fs.readFileSync(filePath)
    s3.putObject(
      {
        Body: fileContent,
        Bucket: bucket,
        Key: newPath,
        ACL: 'public-read'
      },
      (err, data) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      }
    )
  })
}