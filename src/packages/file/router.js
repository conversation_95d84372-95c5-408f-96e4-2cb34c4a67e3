var express = require("express"),
  router = express.Router();
import { auth, validator } from "../../middlewares";
import FileCtrl from "./controller";

/**
 * @api {post} /files/image Upload image
 * @apiVersion 1.0.0
 * @apiName Upload image
 * @apiGroup Files
 *
 * @apiParam {File} image
 *
 * @apiSuccess {String} fileName
 *
 */
router.post("/image", auth, FileCtrl.uploadImage);

/**
 * @api {post} /files/audio Upload audio
 * @apiVersion 1.0.0
 * @apiName Upload audio
 * @apiGroup Files
 *
 * @apiParam {File} audio
 *
 * @apiSuccess {String} fileName
 *
 */
router.post("/audio", auth, FileCtrl.uploadAudio);

module.exports = router;
