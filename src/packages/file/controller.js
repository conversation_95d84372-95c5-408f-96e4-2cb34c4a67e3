const multer = require("multer");
import Constants from "./constants";
import path from "path";
import { response, randomFileNameByExtension, getFileUrl, getAudioFileUrl } from "../../common/utils";
import { executeFile, executeAudioFile } from "../../common/file_utils";

const imageStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, Constants.ImageTempDirectory);
  },
  filename: function (req, file, cb) {
    const extension = path.extname(file.originalname);
    cb(null, randomFileNameByExtension(extension));
  },
});

const upload = multer({
  storage: imageStorage,
  limits: { fileSize: (Constants.MaxUploadFileSize * 1024 * 1024) / 1000 },
}).single("image");

async function uploadImage(req, res, next) {
  upload(req, res, async (err) => {
    if (err) {
      next(err);
    } else {
      if (req.file) {
        try {
          const newPath = await executeFile(req.file.filename, req.file.path);
          res.json(response(true, { name: newPath, url: getFileUrl(newPath) }));
        } catch (error) {
          next(error);
        }
      } else {
        res.status(400).json(response(false, {}, __("Empty image")));
      }
    }
  });
}

const uploadAudioFile = multer({
  storage: imageStorage,
  limits: { fileSize: (Constants.MaxUploadFileSize * 1024 * 1024) / 1000 },
}).single("audio");

async function uploadAudio(req, res, next) {
  uploadAudioFile(req, res, async (err) => {
    if (err) {
      next(err);
    } else {
      if (req.file) {
        try {
          const newPath = await executeAudioFile(req.file.filename, req.file.path);
          res.json(response(true, { name: newPath, url: getAudioFileUrl(newPath) }));
        } catch (error) {
          next(error);
        }
      } else {
        res.status(400).json(response(false, {}, __("Empty image")));
      }
    }
  });
}

export default {
  uploadImage,
  uploadAudio
};
