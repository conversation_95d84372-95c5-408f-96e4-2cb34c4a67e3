var express = require("express"),
  router = express.Router();
import Validation from "./validation";
import { admin, auth, validator } from "../../middlewares";
import JuzuCtrl from "./controller";

/**
 * @api {post} /juzus Add Juzu
 * @apiVersion 1.0.0
 * @apiName Add Juzu
 * @apiGroup Juzu
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiParam {Number} number
 * @apiParam {String} title
 *
 *
 */
router.post(
  "/",
  Validation.validateAddJuzu,
  validator,
  admin,
  JuzuCtrl.add
);

/**
 * @api {put} /juzus/:id Edit Juzu
 * @apiVersion 1.0.0
 * @apiName Edit Juzu
 * @apiGroup Juzu
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiParam {Number} [number]
 * @apiParam {String} [title]
 *
 *
 */
router.put(
  "/:id",
  Validation.validateEditJuzu,
  validator,
  admin,
  JuzuCtrl.edit
);

/**
 * @api {delete} /juzus/:id Delete Juzu
 * @apiVersion 1.0.0
 * @apiName Delete Juzu
 * @apiGroup Juzu
 *
 * @apiHeader Authorization Bearer token
 *
 *
 */
router.delete(
  "/:id",
  Validation.validateDetailJuzu,
  validator,
  admin,
  JuzuCtrl.remove
);

/**
 * @api {get} /juzus Get Juzus
 * @apiVersion 1.0.0
 * @apiName  Get Juzus
 * @apiGroup Juzu
 *
 *
 */
router.get(
  "/",
  JuzuCtrl.list
);

module.exports = router;
