const { body, param } = require('express-validator')
import _ from 'lodash'
import Services from './service'
import SurahServices from '../surah/service'
import AyahServices from '../ayah/service'

export default {
  validateAddJuzu: [
    body('id').exists().withMessage('id is required'),
    body('title.ar')
      .exists()
      .withMessage('title.ar is required')
      .notEmpty()
      .withMessage('title.ar is not blank'),
    body('title.sw')
      .exists()
      .withMessage('title.sw is required')
      .notEmpty()
      .withMessage('title.sw is not blank'),
    body('items').exists().withMessage('items is required').isArray().withMessage('items is array'),
    body('items.*.from')
      .exists()
      .withMessage('items.from is required')
      .isObject()
      .withMessage('items.from must be an object'),
    body('items.*.from.surah')
      .exists()
      .withMessage('items.from.surah is required')
      .custom(SurahServices.validateSurah),
    body('items.*.from.ayah')
      .exists()
      .withMessage('items.from.ayah is required')
      .custom(AyahServices.validateAyah),
    body('items.*.to')
      .exists()
      .withMessage('items.to is required')
      .isObject()
      .withMessage('items.to must be an object'),
    body('items.*.to.surah')
      .exists()
      .withMessage('items.to.surah is required')
      .custom(SurahServices.validateSurah),
    body('items.*.to.ayah')
      .exists()
      .withMessage('items.to.ayah is required')
      .custom(AyahServices.validateAyah),
    body('items.*').custom(Services.validateFromToRange)
  ],

  validateEditJuzu: [
    body('title').optional().isObject().withMessage('title is object'),
    body('title.ar')
      .exists()
      .withMessage('title.ar is required')
      .notEmpty()
      .withMessage('title.ar is not blank'),
    body('title.sw')
      .exists()
      .withMessage('title.sw is required')
      .notEmpty()
      .withMessage('title.sw is not blank'),
    body('items').exists().withMessage('items is required').isArray().withMessage('items is array'),
    body('items.*.from')
      .exists()
      .withMessage('items.from is required')
      .isObject()
      .withMessage('items.from must be an object'),
    body('items.*.from.surah')
      .exists()
      .withMessage('items.from.surah is required')
      .custom(SurahServices.validateSurah),
    body('items.*.from.ayah')
      .exists()
      .withMessage('items.from.ayah is required')
      .custom(AyahServices.validateAyah),
    body('items.*.to')
      .exists()
      .withMessage('items.to is required')
      .isObject()
      .withMessage('items.to must be an object'),
    body('items.*.to.surah')
      .exists()
      .withMessage('items.to.surah is required')
      .custom(SurahServices.validateSurah),
    body('items.*.to.ayah')
      .exists()
      .withMessage('items.to.ayah is required')
      .custom(AyahServices.validateAyah),
    body('items.*').custom(Services.validateFromToRange)
  ],

  validateDetailJuzu: [param('id').custom(Services.validateJuzu)]
}
