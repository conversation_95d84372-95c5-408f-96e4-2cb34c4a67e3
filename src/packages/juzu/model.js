var mongoose = require("mongoose");
var Schema = mongoose.Schema;
import _ from "lodash";

var <PERSON>zu = new Schema(
  {
    id: { type: String, required: true, unique: true },
    title: {
      ar: { type: String, required: true },
      sw: { type: String, required: true }
    },
    items: [
      {
        surah: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Surah",
          required: true
        },
        ayahs: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Ayah"
          }
        ]
      }
    ]
  },
  {
    toObject: {
      transform: function (doc, ret, options) {
        delete ret.__v;
        return ret;
      },
    },
    toJSON: {
      transform: function (doc, ret, options) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

module.exports = mongoose.model("<PERSON>zu", <PERSON>zu);
