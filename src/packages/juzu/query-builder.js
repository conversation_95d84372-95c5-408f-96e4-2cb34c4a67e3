export const <PERSON><PERSON><PERSON>ggregate = [
  {
    $unwind: {
      path: '$items'
    }
  },
  {
    $lookup: {
      from: 'surahs',
      localField: 'items.surah',
      foreignField: '_id',
      pipeline: [
        {
          $project: {
            __v: 0
          }
        }
      ],
      as: 'items.surah'
    }
  },
  {
    $unwind: {
      path: '$items.surah'
    }
  },
  {
    $lookup: {
      from: 'ayahs',
      localField: 'items.ayahs',
      foreignField: '_id',
      pipeline: [
        {
          $project: {
            __v: 0,
            surah: 0
          }
        }
      ],
      as: 'items.ayahs'
    }
  },
  {
    $group: {
      _id: '$_id',
      items: {
        $push: '$items'
      }
    }
  },
  {
    $lookup: {
      from: 'juzus',
      localField: '_id',
      foreignField: '_id',
      as: 'juzuDetails'
    }
  },
  {
    $unwind: {
      path: '$juzuDetails'
    }
  },
  {
    $addFields: {
      'juzuDetails.items': '$items'
    }
  },
  {
    $replaceRoot: {
      newRoot: '$juzuDetails'
    }
  },
  {
    $project: {
      __v: 0,
      'items._id': 0
    }
  },
  {
    $sort: {
      id: 1
    }
  }
]
