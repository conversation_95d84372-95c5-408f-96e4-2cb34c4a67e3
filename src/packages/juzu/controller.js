import _ from "lodash";
import { response } from "../../common/utils";
import Services from './service'

const add = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["id", "title", "items"]);
    const juzu = await Services.addJuzu(body);
    res.json(response(true, juzu));
  } catch (error) {
    next(error);
  }
};

const edit = async (req, res, next) => {
  try {
    let body = _.pick(req.body, [ "title", "items"]);
    const juzu = await Services.editJuzu(req.params.id, body);
    res.json(response(true, juzu));
  } catch (error) {
    next(error);
  }
};

const remove = async (req, res, next) => {
  try {
    await Services.removeJuzu(req.params.id);
    res.json(response(true));
  } catch (error) {
    next(error);
  }
};

const list = async (req, res, next) => {
  try {
    const items = await Services.getJuzus();
    res.json(response(true, items));
  } catch (error) {
    next(error);
  }
};

export default {
  add,
  edit,
  remove,
  list
};
