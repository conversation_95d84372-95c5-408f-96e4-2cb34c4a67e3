import Juzu from './model'
import Ayah from '../ayah/model'
import Surah from '../surah/model'

import { JuzuAggregate } from './query-builder'
const ObjectId = require('mongoose').Types.ObjectId
import AyahServices from '../ayah/service'
import { decorateJuzu } from './helper'

const addJuzu = async (body) => {
  try {
    const { id, title, items } = body
    const existed = await _checkIdForCreate(id)
    if (existed) {
      throw new Error(`ID ${id} is existed.`)
    }

    // Transform from/to items to surah/ayahs structure
    const transformedItems = await transformFromToItems(items)

    const juzu = new Juzu({ id, title, items: transformedItems })
    let item = await juzu.save()
    return getJuzuById(item._id)
  } catch (error) {
    throw error
  }
}

const editJuzu = async (id, body) => {
  try {
    // If items are provided, transform them
    if (body.items) {
      body.items = await transformFromToItems(body.items)
    }

    await Juzu.updateOne({ _id: id }, { $set: body })
    return getJuzuById(id)
  } catch (error) {
    throw error
  }
}

const removeJuzu = async (id) => {
  return Juzu.deleteOne({ _id: id })
}

const getJuzuById = async (id) => {
  try {
    const results = await Juzu.aggregate([{ $match: { _id: ObjectId(id) } }, ...JuzuAggregate])
    return results.length > 0 ? decorateJuzu(results[0]) : null
  } catch (error) {
    throw error
  }
}

const validateJuzu = async (juzu) => {
  try {
    if (!juzu || juzu.length == 0) {
      throw 'juzu is required'
    }
    const item = await Juzu.findOne({ _id: juzu })
    if (!item) {
      throw juzu + ' not  found'
    }
  } catch (error) {
    throw error
  }
}

const getJuzus = async () => {
  try {
    const items = await Juzu.aggregate([...JuzuAggregate])
    let results = []
    items.forEach((item) => {
      results.push(decorateJuzu(item))
    })
    return results
  } catch (error) {
    throw error
  }
}

const validateFromToRange = async (item) => {
  try {
    const { from, to } = item

    // Validate that from.ayah belongs to from.surah
    const fromIsCorrect = await AyahServices.checkAyahInSurah(from.ayah, from.surah)
    if (!fromIsCorrect) {
      throw `From ayah ${from.ayah} is not in surah ${from.surah}`
    }

    // Validate that to.ayah belongs to to.surah
    const toIsCorrect = await AyahServices.checkAyahInSurah(to.ayah, to.surah)
    if (!toIsCorrect) {
      throw `To ayah ${to.ayah} is not in surah ${to.surah}`
    }

    return true
  } catch (error) {
    throw error
  }
}

const transformFromToItems = async (requestItems) => {
  try {
    const transformedItems = []

    for (const item of requestItems) {
      const { from, to } = item

      // Get all ayahs in the range from 'from' to 'to'
      const ayahsInRange = await getAyahsInRange(from, to)

      // Group ayahs by surah
      const ayahsBySurah = {}
      ayahsInRange.forEach((ayah) => {
        const surahId = ayah.surah.toString()
        if (!ayahsBySurah[surahId]) {
          ayahsBySurah[surahId] = []
        }
        ayahsBySurah[surahId].push(ayah._id)
      })

      // Create items for each surah
      for (const [surahId, ayahIds] of Object.entries(ayahsBySurah)) {
        transformedItems.push({
          surah: surahId,
          ayahs: ayahIds
        })
      }
    }

    return transformedItems
  } catch (error) {
    throw error
  }
}

// Helper function to extract numeric part from ayah ID (e.g., "A1001" -> 1001)
const getAyahNumericId = (ayahId) => {
  return parseInt(ayahId.substring(1)) // Remove 'A' and convert to number
}

const getAyahsInRange = async (from, to) => {
  try {
    // First, get the ayah details to understand the range
    const fromAyah = await Ayah.findById(from.ayah).select('id surah')
    const toAyah = await Ayah.findById(to.ayah).select('id surah')

    if (!fromAyah || !toAyah) {
      throw 'Invalid ayah range'
    }

    const fromNumericId = getAyahNumericId(fromAyah.id)
    const toNumericId = getAyahNumericId(toAyah.id)

    // If both ayahs are in the same surah
    if (from.surah.toString() === to.surah.toString()) {
      // Use aggregation pipeline to properly sort by numeric value
      const ayahs = await Ayah.aggregate([
        {
          $match: {
            surah: ObjectId(from.surah)
          }
        },
        {
          $addFields: {
            numericId: {
              $toInt: { $substr: ['$id', 1, -1] } // Extract numeric part
            }
          }
        },
        {
          $match: {
            numericId: {
              $gte: fromNumericId,
              $lte: toNumericId
            }
          }
        },
        {
          $sort: { numericId: 1 }
        },
        {
          $project: {
            numericId: 0 // Remove the temporary field
          }
        }
      ])
      return ayahs
    } else {
      // Get all surahs in the range from 'from' to 'to'
      const fromSurah = await Surah.findById(from.surah).select('id')
      const toSurah = await Surah.findById(to.surah).select('id')

      if (!fromSurah || !toSurah) {
        throw 'Invalid surah range'
      }

      const fromSurahNumericId = fromSurah.id
      const toSurahNumericId = toSurah.id

      const surahs = await Surah.find({
        id: {
          $gte: fromSurahNumericId,
          $lte: toSurahNumericId
        }
      }).sort({ id: 1 })

      console.log({ surahs })

      const ayahs = []
      for (const surah of surahs) {
        console.log('surah.id', surah.id)
        //i need to query ayahs in the range from 'from' to 'to', if surah is from.surah, then from 'from' to end of surah
        // if surah is to.surah, then from start of surah to 'to'
        // if surah is in between, then from start to end
        if (surah._id.toString() === from.surah) {
          // Use aggregation pipeline to properly sort by numeric value
          const items = await Ayah.aggregate([
            {
              $match: {
                surah: ObjectId(from.surah)
              }
            },
            {
              $addFields: {
                numericId: {
                  $toInt: { $substr: ['$id', 1, -1] } // Extract numeric part
                }
              }
            },
            {
              $match: {
                numericId: {
                  $gte: fromNumericId
                }
              }
            },
            {
              $sort: { numericId: 1 }
            },
            {
              $project: {
                numericId: 0
              }
            }
          ])

          console.log(
            'surah.id === from.surah',
            items.map((ayah) => ayah.id)
          )
          ayahs.push(...items)
        } else if (surah._id.toString() === to.surah) {
          // Use aggregation pipeline to properly sort by numeric value
          const items = await Ayah.aggregate([
            {
              $match: {
                surah: ObjectId(to.surah)
              }
            },
            {
              $addFields: {
                numericId: {
                  $toInt: { $substr: ['$id', 1, -1] } // Extract numeric part
                }
              }
            },
            {
              $match: {
                numericId: {
                  $lte: toNumericId
                }
              }
            },
            {
              $sort: { numericId: 1 }
            },
            {
              $project: {
                numericId: 0
              }
            }
          ])
          console.log(
            'surah.id === to.surah',
            items.map((ayah) => ayah.id)
          )
          ayahs.push(...items)
        } else {
          const items = await Ayah.aggregate([
            {
              $match: { surah: ObjectId(surah._id) }
            },
            {
              $addFields: {
                numericId: {
                  $toInt: { $substr: ['$id', 1, -1] }
                }
              }
            },
            {
              $sort: { numericId: 1 }
            },
            {
              $project: {
                numericId: 0
              }
            }
          ])
          console.log(
            'surah.id === ' + surah.id,
            items.map((ayah) => ayah.id)
          )
          ayahs.push(...items)
        }
      }

      return ayahs
    }
  } catch (error) {
    throw error
  }
}

const _checkIdForCreate = async (id) => {
  try {
    const item = await Juzu.findOne({ id: id })
    return item != null
  } catch (error) {
    throw error
  }
}

export default {
  addJuzu,
  editJuzu,
  removeJuzu,
  getJuzuById,
  validateJuzu,
  getJuzus,
  validateFromToRange,
  transformFromToItems
}
