import _ from "lodash";
var User = require("./model");
import { response } from "../../common/utils";
import Services from './service'

const list = async (req, res, next) => {
  try {
    const ayahs = await Services.getAyahs(req.query);
    res.json(response(true, ayahs));
  } catch (error) {
    next(error);
  }
};

const add = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["id","surah", "title", "content", "explanation", "audio"]);
    const ayah = await Services.addAyah(body);
    res.json(response(true, ayah));
  } catch (error) {
    next(error);
  }
};

const edit = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["surah", "title", "content", "explanation", "audio"]);
    const ayah = await Services.editAyah(req.params.id, body);
    res.json(response(true, ayah));
  } catch (error) {
    next(error);
  }
};

const remove = async (req, res, next) => {
  try {
    await Services.removeAyah(req.params.id);
    res.json(response(true));
  } catch (error) {
    next(error);
  }
};

export default {
  list,
  add,
  edit,
  remove
};
