var mongoose = require('mongoose')
var Schema = mongoose.Schema
import _ from 'lodash'

var Ayah = new Schema(
  {
    id: { type: String, required: true },
    audio: { type: String },
    title: {
      ar: { type: String, required: true },
      arNoDiacritics: { type: String }, // Arabic without diacritics for better search
      sw: { type: String, required: true }
    },
    content: {
      ar: { type: String },
      arNoDiacritics: { type: String }, // Arabic without diacritics for better search
      sw: { type: String }
    },
    explanation: {
      ar: { type: String },
      arNoDiacritics: { type: String }, // Arabic without diacritics for better search
      sw: { type: String }
    },
    surah: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Surah'
    }
  },
  {
    toObject: {
      transform: function (doc, ret, options) {
        delete ret.__v
        return ret
      }
    },
    toJSON: {
      transform: function (doc, ret, options) {
        delete ret.__v
        return ret
      }
    }
  }
)

module.exports = mongoose.model('Ayah', Ayah)
