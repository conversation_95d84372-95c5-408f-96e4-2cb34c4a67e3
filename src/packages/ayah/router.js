var express = require('express'),
  router = express.Router()
import Validation from './validation'
import { admin, auth, validator } from '../../middlewares'
import AyahCtrl from './controller'

/**
 * @api {get} /ayahs Search Ayahs
 * @apiVersion 1.0.0
 * @apiName Search Ayahs
 * @apiGroup Ayah
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiParam {String} [search]
 *
 */
router.get('/', AyahCtrl.list)

/**
 * @api {post} /ayahs Add Ayah
 * @apiVersion 1.0.0
 * @apiName Add Ayah
 * @apiGroup Ayah
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiParam {String} surah
 * @apiParam {Number} number
 * @apiParam {String} title
 * @apiParam {String} content
 *
 */
router.post('/', Validation.validateAddAyah, validator, admin, AyahCtrl.add)

/**
 * @api {put} /ayahs/:id Edit Ayah
 * @apiVersion 1.0.0
 * @apiName Edit Ayah
 * @apiGroup Ayah
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiParam {String} [surah]
 * @apiParam {Number} [number]
 * @apiParam {String} [title]
 * @apiParam {String} [content]
 *
 */
router.put('/:id', Validation.validateEditAyah, validator, admin, AyahCtrl.edit)

/**
 * @api {delete} /ayahs/:id Delete Ayah
 * @apiVersion 1.0.0
 * @apiName Delete Ayah
 * @apiGroup Ayah
 *
 * @apiHeader Authorization Bearer token
 *
 *
 */
router.delete('/:id', Validation.validateDetailAyah, validator, admin, AyahCtrl.remove)

module.exports = router
