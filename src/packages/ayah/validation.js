const {
  body,
  check,
  sanitizeBody,
  param,
  query,
} = require("express-validator");
import _ from "lodash";
import Services from './service'
import SurahServices from '../surah/service'

export default {
  validateAddAyah: [
    body("id").exists().withMessage("id is required").notEmpty().withMessage("id is not blank"),
    body("surah").exists().withMessage("surah is required").custom(SurahServices.validateSurah),
    body("title.ar").exists().withMessage("title.ar is required").notEmpty().withMessage("title.ar is not blank"),
    body("title.sw").exists().withMessage("title.sw is required").notEmpty().withMessage("title.sw is not blank"),
    body("content.ar").exists().withMessage("content.ar is required").notEmpty().withMessage("content.ar is not blank"),
    body("content.sw").exists().withMessage("content.sw is required").notEmpty().withMessage("content.sw is not blank"),
    body("explanation.ar").exists().withMessage("explanation.ar is required").notEmpty().withMessage("explanation.ar is not blank"),
    body("explanation.sw").exists().withMessage("explanation.sw is required").notEmpty().withMessage("explanation.sw is not blank"),
  ],
  validateEditAyah: [
    param("id").custom(Services.validateAyah),
    body("surah").optional().custom(SurahServices.validateSurah),
    body("title").optional().isObject().withMessage("title is object"),
    body("title.ar").exists().withMessage("title.ar is required").notEmpty().withMessage("title.ar is not blank"),
    body("title.sw").exists().withMessage("title.sw is required").notEmpty().withMessage("title.sw is not blank"),
    body("content").optional().isObject().withMessage("content is object"),
    body("content.ar").exists().withMessage("content.ar is required").notEmpty().withMessage("content.ar is not blank"),
    body("content.sw").exists().withMessage("content.sw is required").notEmpty().withMessage("content.sw is not blank"),
    body("explanation").optional().isObject().withMessage("explanation is object"),
    body("explanation.ar").exists().withMessage("explanation.ar is required").notEmpty().withMessage("explanation.ar is not blank"),
    body("explanation.sw").exists().withMessage("explanation.sw is required").notEmpty().withMessage("explanation.sw is not blank"),
  ],

  validateDetailAyah: [
    param("id").custom(Services.validateAyah),
  ],
};
