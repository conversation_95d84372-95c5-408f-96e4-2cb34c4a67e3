import Constants from './constants'
import Ayah from './model'
import Surah from '../surah/model'
import ayahList from '../../data/ayahs_list.json'

import { AyahAggregate } from './query-builder'
const ObjectId = require('mongoose').Types.ObjectId
import { decorateAyah } from './helper'

const addAyah = async (body) => {
  try {
    const { id, surah, title, content, audio, explanation } = body
    const ayah = new Ayah({ id, surah, title, content, audio, explanation })
    let item = await ayah.save()
    return _getAyahById(item._id)
  } catch (error) {
    throw error
  }
}

const editAyah = async (id, body) => {
  try {
    await Ayah.updateOne({ _id: id }, { $set: body })
    return _getAyahById(id)
  } catch (error) {
    throw error
  }
}

const removeAyah = async (id) => {
  return Ayah.deleteOne({ _id: id })
}

const _getAyahById = async (_id) => {
  try {
    const results = await Ayah.aggregate([{ $match: { _id: ObjectId(_id) } }, ...AyahAggregate])
    return results.length > 0 ? decorateAyah(results[0]) : null
  } catch (error) {
    throw error
  }
}

const validateAyah = async (ayah) => {
  try {
    if (!ayah || ayah.length == 0) {
      throw 'ayah is required'
    }
    const item = await Ayah.findOne({ _id: ayah })
    if (!item) {
      throw 'ayah ' + ayah + ' not  found'
    }
  } catch (error) {
    throw error
  }
}

const checkAyahInSurah = async (ayahId, surahId) => {
  try {
    const item = await Ayah.findOne({ _id: ayahId, surah: surahId })
    return item != null
  } catch (error) {
    throw error
  }
}

const getAyahs = async (args) => {
  try {
    let query = {}
    if (args.search && args.search.length > 0) {
      query['$or'] = [
        { 'title.ar': { $regex: args.search, $options: 'i' } },
        { 'title.sw': { $regex: args.search, $options: 'i' } },
        { 'content.ar': { $regex: args.search, $options: 'i' } },
        { 'content.sw': { $regex: args.search, $options: 'i' } },
        { 'explanation.ar': { $regex: args.search, $options: 'i' } },
        { 'explanation.sw': { $regex: args.search, $options: 'i' } }
      ]
    }
    const results = await Ayah.aggregate([{ $match: query }, ...AyahAggregate])
    return results.map((e) => decorateAyah(e))
  } catch (error) {
    throw error
  }
}

const _checkIdForCreate = async (id) => {
  try {
    const item = await Ayah.findOne({ id: id })
    return item != null
  } catch (error) {
    throw error
  }
}

const importData = async () => {
  try {
    for (let index = 0; index < ayahList.length; index++) {
      const element = ayahList[index]
      const existed = await _checkIdForCreate(element['ID'])
      if (!existed) {
        const surah = await Surah.findOne({ id: element['surahID'] })
        if (surah) {
          try {
            await addAyah({
              id: element['ID'],
              surah: surah._id,
              title: {
                ar: surah.title['ar'],
                sw: surah.title['sw']
              },
              content: {
                ar: element['arabicText'],
                sw: element['content']
              },
              explanation: {
                ar: element['explanation'],
                sw: element['explanation']
              }
            })
          } catch (error) {
            console.log({ element })
            console.log(error)
          }
        }
      } else {
        await Ayah.updateOne(
          { id: element['ID'] },
          {
            $set: {
              content: {
                ar: element['arabicText'],
                sw: element['content']
              },
              explanation: {
                ar: element['explanation'],
                sw: element['explanation']
              }
            }
          }
        )
      }
    }
  } catch (error) {
    throw error
  }
}

export default {
  addAyah,
  editAyah,
  removeAyah,
  validateAyah,
  checkAyahInSurah,
  getAyahs,
  importData
}
