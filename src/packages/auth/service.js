var jwt = require("jsonwebtoken");
import { verify } from "jsonwebtoken";
import Constants from "../../common/constants";
import _ from "lodash";

const generateUserToken = (userId) => {
  let encoded = { userId };
  return jwt.sign(encoded, Constants.JWTSecret);
};

const decodeToken = (token) => {
  return verify(token, Constants.JWTSecret);
};


export default {
  generateUserToken,
  decodeToken
};
