import _ from "lodash";

const bcrypt = require("bcrypt");
var User = require("../user/model");
import { response } from "../../common/utils";
import Constants from "../../common/constants";
import Service from "./service";
import UserServices from '../user/service'

const _loginUser = async (userId, res) => {
  try {
    const user = await UserServices.getUserById(userId)
    res.json(
      response(
        true,
        {
          token: Service.generateUserToken(userId),
          userInfo: user,
        },
        null
      )
    );
  } catch (error) {
    res.status(500).json(error);
  }
}

const register = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["name", "email", "password"]);
    let item = await UserServices.registerUser(body)
    await _loginUser(item._id, res);
  } catch (error) {
    next(error);
  }
};

const login = (req, res) => {
  User.findOne({ email: req.body.email }, async (err, user) => {
    if (err) return next(err);
    if (bcrypt.compareSync(req.body.password, user.hash)) {
      await _loginUser(user._id, res);
    } else {
      res
        .status(Constants.ApiStatusCodes.Unauthorized)
        .send(response(false, null, __("The password is incorrect.")));
    }
  });
};

const forgotPassword = async (req, res) => {
  try {
    await UserServices.forgotPassword(req.body.email)
    res.json(
      response(
        true,
        {
          message: 'The new password is sent to your email. Please check the email to get new password.'
        },
        null
      )
    );
  } catch (error) {
    next(error);
  }
};

export default {
  register,
  login,
  forgotPassword
};
