const {
  body,
  check,
  sanitizeBody,
  param,
  query,
} = require("express-validator");
import Constants from "../../common/constants";
import User from "../user/model";
import _ from "lodash";
import UserServices from '../user/service'

export default {
  validateRegister: [
    body("name")
      .exists()
      .withMessage("name is required")
      .notEmpty()
      .withMessage("name is not blank"),
    body("email")
      .exists()
      .withMessage("email is required")
      .isEmail()
      .withMessage(__("The Email Address is in an invalid format."))
      .custom(UserServices.checkEmailToRegister),
    body("password")
      .exists()
      .withMessage("password is required")
      .notEmpty()
      .withMessage("password is not blank"),
  ],
  validateLogin: [
    check("email")
      .exists()
      .withMessage("email is required")
      .isEmail()
      .withMessage(__("The Email Address is in an invalid format."))
      .custom((value) => {
        return User.findOne({
          email: value,
        }).then((user) => {
          if (!user) {
            return Promise.reject(__("The email is not registered."));
          } else if (!user.enabled) {
            return Promise.reject(__("The account is blocked."));
          }
        });
      }),
    check("password").exists().withMessage("password is required"),
  ],
  validateForgotPassword: [
    check("email")
      .exists()
      .withMessage("email is required")
      .isEmail()
      .withMessage(__("The Email Address is in an invalid format."))
      .custom((value) => {
        return User.findOne({
          email: value,
        }).then((user) => {
          if (!user) {
            return Promise.reject(__("The email is not registered."));
          } else if (!user.enabled) {
            return Promise.reject(__("The account is blocked."));
          }
        });
      }),
  ],
};
