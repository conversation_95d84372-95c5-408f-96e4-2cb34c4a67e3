var express = require("express"),
  router = express.Router();
import Validation from "./validation";
import { auth, validator } from "../../middlewares";
import AuthCtrl from "./controller";

/**
 * @api {post} /auth/register Register
 * @apiVersion 1.0.0
 * @apiName Register
 * @apiGroup Auth
 *
 * @apiParam {String} name
 * @apiParam {String} email
 * @apiParam {String} password
 *
 */
router.post(
  "/register",
  Validation.validateRegister,
  validator,
  AuthCtrl.register
);

/**
 * @api {post} /auth/login Login
 * @apiVersion 1.0.0
 * @apiName Login
 * @apiGroup Auth
 *
 * @apiParam {String} email
 * @apiParam {String} password
 *
 * @apiSuccess {Object} userInfo
 * @apiSuccess {String} token
 *
 */
router.post("/login", Validation.validateLogin, validator, AuthCtrl.login);

/**
 * @api {post} /auth/forgot-password Forgot Password
 * @apiVersion 1.0.0
 * @apiName Forgot Password
 * @apiGroup Auth
 *
 * @apiParam {String} email
 *
 */
router.post("/forgot-password", Validation.validateForgotPassword, validator, AuthCtrl.forgotPassword);

module.exports = router;
