var mongoose = require("mongoose");
var Schema = mongoose.Schema;
import Constants from "./constants";
import _ from "lodash";
var User = new Schema(
  {
    name: { type: String, default: "" },
    email: { type: String, required: true },
    avatar: { type: String },
    role: { type: String, default: Constants.Role.User },
    hash: { type: String, default: "" },
    lastReadAyah: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Ayah",
    },
    enabled: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
  },
  {
    toObject: {
      transform: function (doc, ret, options) {
        delete ret.hash;
        delete ret.__v;
        return ret;
      },
    },
    toJSON: {
      transform: function (doc, ret, options) {
        delete ret.hash;
        delete ret.__v;
        return ret;
      },
    },
  }
);

module.exports = mongoose.model("User", User);
