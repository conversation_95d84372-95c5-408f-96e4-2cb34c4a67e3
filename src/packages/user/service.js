import Constants from './constants';
import User from './model'
const bcrypt = require("bcrypt");
import { UserAggregate } from './query-builder'
const ObjectId = require("mongoose").Types.ObjectId;
import { decorateUser } from './helper'
var randomstring = require("randomstring");
import { sendEmail } from '../../common/utils'
import * as EmailTemplate from '../../common/email_templates/index'

const registerUser = async (body) => {
    try {
        const { name, email, password } = body
        const hash = bcrypt.hashSync(password, 10);
        const user = new User({ name, email, hash, role: Constants.Role.User });
        let item = await user.save();
        return getUserById(item._id)
    } catch (error) {
        throw error
    }
};

const forgotPassword = async (email) => {
    try {
        const newPassword = randomstring.generate({
            length: 6,
            charset: 'alphanumeric'
        });
        const hash = bcrypt.hashSync(newPassword, 10);
        await User.updateOne({ email: email }, { $set: { hash } })
        const template = EmailTemplate.ForgotPassword(newPassword)
        await sendEmail(email, template.title, template.html)
    } catch (error) {
        throw error
    }
};

const editUser = async (params, userId) => {
    try {
        await User.updateOne({ _id: userId }, { $set: params });
        return getUserById(userId);
    } catch (error) {
        throw error
    }
};

const getUserById = async (id) => {
    try {
        const userAggregate = [
            { $match: { _id: ObjectId(id) } },
            ...UserAggregate,
        ]
        const results = await User.aggregate(userAggregate);
        return results.length > 0 ? decorateUser(results[0]) : null
    } catch (error) {
        throw error
    }
}

const changePassword = async (userId, oldPassword, newPassword) => {
    try {
        let user = await User.findById(userId)
        if (bcrypt.compareSync(oldPassword, user.hash)) {
            const hash = bcrypt.hashSync(newPassword, 10);
            await User.updateOne({ _id: userId }, { $set: { hash } })
        } else {
            throw new Error(__("The password is incorrect."))
        }
    } catch (error) {
        throw error
    }
}

const checkEmailToRegister = async (email) => {
    try {
        const item = await User.findOne({ email: email });
        if (item) {
            throw email + " is existed";
        }
    } catch (error) {
        throw error;
    }
};

export default {
    registerUser,
    editUser,
    changePassword,
    getUserById,
    checkEmailToRegister,
    forgotPassword
}