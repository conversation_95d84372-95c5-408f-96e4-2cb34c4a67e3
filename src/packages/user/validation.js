const {
  body,
  check,
  sanitizeBody,
  param,
  query,
} = require("express-validator");
import _ from "lodash";
import Constants from './constants'

export default {
  validateUpdateUser: [
    body("name").optional().notEmpty().withMessage("name is not blank"),
  ],
  validateChangePassword: [
    body("oldPassword").exists().withMessage("oldPassword is required").notEmpty().withMessage("oldPassword is not blank"),
    body("newPassword").exists().withMessage("newPassword is required").notEmpty().withMessage("newPassword is not blank"),
  ],
};
