import _ from "lodash";
var User = require("./model");
import { response } from "../../common/utils";
import Services from './service'

const update = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["avatar", "name", "lastReadAyah"]);
    const user = await Services.editUser(body, req.userId);
    res.json(response(true, user));
  } catch (error) {
    next(error);
  }
};

const changePassword = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["oldPassword", "newPassword"]);
    await Services.changePassword(req.userId, body.oldPassword, body.newPassword)
    res.json(response(true, null));
  } catch (error) {
    next(error);
  }
};

const addDeviceToken = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["deviceToken"]);
    const isExisted = await User.findOne({ _id: req.userId, "devices": { $in: [body.deviceToken] } })
    if (!isExisted) {
      await User.updateOne({ _id: req.userId }, { $push: { devices: body.deviceToken } });
    }
    res.json(response(true, true));
  } catch (error) {
    next(error);
  }
};

const getMyProfile = async (req, res, next) => {
  try {
    const user = await Services.getUserById(req.userId);
    res.json(response(true, user));
  } catch (error) {
    next(error);
  }
};

export default {
  update,
  changePassword,
  addDeviceToken,
  getMyProfile
};
