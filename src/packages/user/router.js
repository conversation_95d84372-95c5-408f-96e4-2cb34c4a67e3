var express = require("express"),
  router = express.Router();
import Validation from "./validation";
import { admin, auth, validator } from "../../middlewares";
import UsersCtrl from "./controller";

/**
 * @api {put} /users Update My Profile
 * @apiVersion 1.0.0
 * @apiName Update My Profile
 * @apiGroup Users
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiParam {String} [avatar] fileName after upload
 * @apiParam {String} [name]
 *
 * @apiSuccess {Object} userInfo
 *
 */
router.put(
  "/",
  Validation.validateUpdateUser,
  validator,
  auth,
  UsersCtrl.update
);

/**
 * @api {post} /users/change-password Change Password
 * @apiVersion 1.0.0
 * @apiName Change Password
 * @apiGroup Users
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiParam {String} oldPassword
 * @apiParam {String} newPassword
 *
 *
 */
router.post(
  "/change-password",
  Validation.validateChangePassword,
  validator,
  auth,
  UsersCtrl.changePassword
);


/**
 * @api {get} /users/me Get My Profile
 * @apiVersion 1.0.0
 * @apiName Get My Profile
 * @apiGroup Users
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiSuccess {Object} userInfo
 *
 */
router.get(
  "/me",
  auth,
  UsersCtrl.getMyProfile
);

module.exports = router;
