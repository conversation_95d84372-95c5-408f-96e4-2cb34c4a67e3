
export const UserAggregate = [
    {
        $project: {
            name: 1,
            email: 1,
            avatar: 1,
            role: 1,
            enabled: 1,
            lastReadAyah: 1,
        },
    },
    {
        $lookup: {
            from: 'ayahs',
            localField: 'lastReadAyah',
            foreignField: '_id',
            pipeline: [
                {
                    $project: {
                        __v: 0,
                    }
                }
            ],
            as: 'lastReadAyah'
        }
    },
    {
        $unwind: {
            path: '$lastReadAyah',
            preserveNullAndEmptyArrays: true
        }
    },
]