var mongoose = require("mongoose");
var Schema = mongoose.Schema;
import _ from "lodash";

var Surah = new Schema(
  {
    id: { type: String, required: true, unique: true },
    leadingFatiha: { type: Boolean, default: false },
    title: {
      ar: { type: String, required: true },
      sw: { type: String, required: true }
    },
  },
  {
    toObject: {
      transform: function (doc, ret, options) {
        delete ret.__v;
        return ret;
      },
    },
    toJSON: {
      transform: function (doc, ret, options) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

module.exports = mongoose.model("Surah", Surah);
