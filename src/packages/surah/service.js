import Constants from './constants'
import Surah from './model'
import { decorateSurah } from './helper'
import { SurahAggregate } from './query-builder'
const ObjectId = require('mongoose').Types.ObjectId
import suralList from '../../data/surah_list.json'

const addSurah = async (body) => {
  try {
    const { id, title } = body
    const existed = await _checkIdForCreate(id)
    if (existed) {
      throw new Error(`ID ${id} is existed.`)
    }
    const surah = new Surah({ id, title })
    let item = await surah.save()
    return getSurahById(item._id)
  } catch (error) {
    throw error
  }
}

const editSurah = async (id, body) => {
  try {
    await Surah.updateOne({ _id: id }, { $set: body })
    return getSurahById(id)
  } catch (error) {
    throw error
  }
}

const removeSurah = async (id) => {
  return Surah.deleteOne({ _id: id })
}

const getSurahById = async (id) => {
  try {
    const results = await Surah.aggregate([{ $match: { _id: ObjectId(id) } }, ...SurahAggregate])
    return results.length > 0 ? decorateSurah(results[0]) : null
  } catch (error) {
    throw error
  }
}

const validateSurah = async (surah) => {
  try {
    if (!surah || surah.length == 0) {
      throw 'surah is required'
    }
    const item = await Surah.findOne({ _id: surah })
    if (!item) {
      throw 'surah ' + surah + ' not  found'
    }
  } catch (error) {
    console.log(error)
    throw error
  }
}

const validateSurahs = async (surahs, { req, location, path }) => {
  return Promise.all([
    ...surahs.map(async (surah) => {
      return validateSurah(surah)
    })
  ])
}

const getSurahs = async () => {
  try {
    const items = await Surah.aggregate([...SurahAggregate])
    let results = []
    items.forEach((item) => {
      results.push(decorateSurah(item))
    })
    return results
  } catch (error) {
    throw error
  }
}

const _checkIdForCreate = async (id) => {
  try {
    const item = await Surah.findOne({ id: id })
    return item != null
  } catch (error) {
    throw error
  }
}

const importData = async () => {
  try {
    for (let index = 0; index < suralList.length; index++) {
      const element = suralList[index]
      const existed = await _checkIdForCreate(element['ID'])
      if (!existed) {
        const surah = new Surah({
          id: element['ID'],
          title: {
            ar: element['arabicTitle'],
            sw: element['title']
          }
        })
        await surah.save()
      }
    }
  } catch (error) {
    throw error
  }
}

export default {
  addSurah,
  editSurah,
  removeSurah,
  getSurahById,
  validateSurah,
  getSurahs,
  validateSurahs,
  importData
}
