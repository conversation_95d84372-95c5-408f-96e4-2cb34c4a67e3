import _ from "lodash";
var User = require("./model");
import { response } from "../../common/utils";
import Services from './service'

const add = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["id", "title", "leadingFatiha"]);
    const surah = await Services.addSurah(body);
    res.json(response(true, surah));
  } catch (error) {
    next(error);
  }
};

const edit = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["title", "leadingFatiha"]);
    const surah = await Services.editSurah(req.params.id, body);
    res.json(response(true, surah));
  } catch (error) {
    next(error);
  }
};

const remove = async (req, res, next) => {
  try {
    await Services.removeSurah(req.params.id);
    res.json(response(true));
  } catch (error) {
    next(error);
  }
};

const list = async (req, res, next) => {
  try {
    const items = await Services.getSurahs();
    res.json(response(true, items));
  } catch (error) {
    next(error);
  }
};

export default {
  add,
  edit,
  remove,
  list
};
