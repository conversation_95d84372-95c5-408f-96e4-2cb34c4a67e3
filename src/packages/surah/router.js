var express = require("express"),
  router = express.Router();
import Validation from "./validation";
import { admin, auth, validator } from "../../middlewares";
import SurahCtrl from "./controller";

/**
 * @api {post} /surahs Add Surah
 * @apiVersion 1.0.0
 * @apiName Add Surah
 * @apiGroup Surah
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiParam {Number} number
 * @apiParam {String} title
 *
 *
 */
router.post(
  "/",
  Validation.validateAddSurah,
  validator,
  admin,
  SurahCtrl.add
);

/**
 * @api {put} /surahs/:id Edit Surah
 * @apiVersion 1.0.0
 * @apiName Edit Surah
 * @apiGroup Surah
 *
 * @apiHeader Authorization Bearer token
 *
 * @apiParam {Number} [number]
 * @apiParam {String} [title]
 *
 *
 */
router.put(
  "/:id",
  Validation.validateEditSurah,
  validator,
  admin,
  SurahCtrl.edit
);

/**
 * @api {delete} /surahs/:id Delete Surah
 * @apiVersion 1.0.0
 * @apiName Delete Surah
 * @apiGroup Surah
 *
 * @apiHeader Authorization Bearer token
 *
 *
 */
router.delete(
  "/:id",
  Validation.validateDetailSurah,
  validator,
  admin,
  SurahCtrl.remove
);

/**
 * @api {get} /surahs Get Surahs
 * @apiVersion 1.0.0
 * @apiName  Get Surahs
 * @apiGroup Surah
 *
 *
 */
router.get(
  "/",
  SurahCtrl.list
);

module.exports = router;
