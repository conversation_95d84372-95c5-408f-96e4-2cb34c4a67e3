
export const SurahAggregate = [
    {
        $project: {
            __v: 0
        },
    },
    {
        $lookup: {
            from: "ayahs",
            localField: "_id",
            foreignField: "surah",
            "pipeline": [{
                $project: {
                    surah: 0,
                },
            }],
            as: "ayahs",
        },
    },
    {
        $lookup: {
            from: "juzus",
            localField: "_id",
            foreignField: "items.surah",
            "pipeline": [{
                $project: {
                    items: 0,
                },
            }],
            as: "juzus",
        },
    },
]