const {
  body,
  check,
  sanitizeBody,
  param,
  query,
} = require("express-validator");
import _ from "lodash";
import Services from './service'

export default {
  validateAddSurah: [
    body("id").exists().withMessage("id is required"),
    body("leadingFatiha").exists().withMessage("leadingFatiha is required").isBoolean().withMessage('leadingFatiha must be true/false'),
    body("title.ar").exists().withMessage("title.ar is required").notEmpty().withMessage("title.ar is not blank"),
    body("title.sw").exists().withMessage("title.sw is required").notEmpty().withMessage("title.sw is not blank"),
  ],

  validateEditSurah: [
    param("id").custom(Services.validateSurah),
    body("leadingFatiha").optional().isBoolean().withMessage('leadingFatiha must be true/false'),
    body("title").optional().isObject().withMessage("title is object"),
    body("title.ar").exists().withMessage("title.ar is required").notEmpty().withMessage("title.ar is not blank"),
    body("title.sw").exists().withMessage("title.sw is required").notEmpty().withMessage("title.sw is not blank"),
  ],

  validateDetailSurah: [
    param("id").custom(Services.validateSurah),
  ],
};
