import Constants from './constants';
import Book from './model'
import { decorateBook } from './helper'

import { BookAggregate } from './query-builder'
const ObjectId = require("mongoose").Types.ObjectId;

const addBook = async (body) => {
    try {
        const book = new Book(body);
        let item = await book.save();
        return getBookById(item._id)
    } catch (error) {
        throw error
    }
};

const editBook = async (id, body) => {
    try {
        await Book.updateOne({ _id: id }, { $set: body })
        return getBookById(id)
    } catch (error) {
        throw error
    }
};

const removeBook = async (id) => {
    return Book.deleteOne({ _id: id })
};

const getBookById = async (id) => {
    try {
        const results = await Book.aggregate([
            { $match: { _id: ObjectId(id) } },
            ...BookAggregate,
        ]);
        return results.length > 0 ? decorateBook(results[0]) : null
    } catch (error) {
        throw error
    }
}

const validateBook = async (book) => {
    try {
        if (!book || book.length == 0) {
            throw "book is required";
        }
        if (!ObjectId.isValid(book)) {
            throw book + " is invalid ID";
        }
        const item = await Book.findById(book);
        if (!item) {
            throw book + " not  found";
        }
    } catch (error) {
        throw error;
    }
};

const getBooks = async () => {
    try {
        const items = await Book.aggregate([
            ...BookAggregate,
        ])
        let results = []
        items.forEach((item) => {
            results.push(decorateBook(item))
        })
        return results
    } catch (error) {
        throw error
    }
};

export default {
    addBook,
    editBook,
    removeBook,
    getBookById,
    validateBook,
    getBooks
}