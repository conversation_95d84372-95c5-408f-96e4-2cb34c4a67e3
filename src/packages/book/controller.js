import _ from "lodash";
var User = require("./model");
import { response } from "../../common/utils";
import Services from './service'

const add = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["name", "image", "chapters"]);
    const book = await Services.addBook(body);
    res.json(response(true, book));
  } catch (error) {
    next(error);
  }
};

const edit = async (req, res, next) => {
  try {
    let body = _.pick(req.body, ["name", "image", "chapters"]);
    const book = await Services.editBook(req.params.id, body);
    res.json(response(true, book));
  } catch (error) {
    next(error);
  }
};

const remove = async (req, res, next) => {
  try {
    const book = await Services.removeBook(req.params.id);
    res.json(response(true, book));
  } catch (error) {
    next(error);
  }
};

const list = async (req, res, next) => {
  try {
    const items = await Services.getBooks();
    res.json(response(true, items));
  } catch (error) {
    next(error);
  }
};

export default {
  add,
  edit,
  remove,
  list
};
