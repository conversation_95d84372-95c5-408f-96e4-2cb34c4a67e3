const {
  body,
  check,
  sanitizeBody,
  param,
  query,
} = require("express-validator");
import _ from "lodash";
import Services from './service'

export default {
  validateAddBook: [
    body("name").exists().withMessage("name is required").notEmpty().withMessage("name is not empty"),
    body("image").exists().withMessage("image is required"),
    body("chapters").exists().withMessage("chapters is required").isArray().withMessage('chapters must be array'),
    body("chapters.*.title").exists().withMessage("chapters.title is required").notEmpty().withMessage("chapters.title is not blank"),
    body("chapters.*.content").exists().withMessage("chapters.content is required").notEmpty().withMessage("chapters.content is not blank"),
  ],

  validateEditBook: [
    body("name").optional().notEmpty().withMessage("name is not empty"),
    body("chapters").optional().isArray().withMessage('chapters must be array'),
    body("chapters.*.title").exists().withMessage("chapters.title is required").notEmpty().withMessage("chapters.title is not blank"),
    body("chapters.*.content").exists().withMessage("chapters.content is required").notEmpty().withMessage("chapters.content is not blank"),
  ],

  validateDetailBook: [
    param("id").custom(Services.validateBook),
  ],
};
