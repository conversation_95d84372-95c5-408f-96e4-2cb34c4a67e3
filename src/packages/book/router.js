var express = require("express"),
  router = express.Router();
import Validation from "./validation";
import { admin, auth, validator } from "../../middlewares";
import BookCtrl from "./controller";

/**
 * @api {post} /books Add Book
 * @apiVersion 1.0.0
 * @apiName Add Book
 * @apiGroup Book
 *
 * @apiHeader Authorization Bearer token
 *
 *
 */
router.post(
  "/",
  Validation.validateAddBook,
  validator,
  admin,
  BookCtrl.add
);

/**
 * @api {put} /books/:id Edit Book
 * @apiVersion 1.0.0
 * @apiName Edit Book
 * @apiGroup Book
 *
 * @apiHeader Authorization Bearer token
 *
 *
 */
router.put(
  "/:id",
  Validation.validateEditBook,
  validator,
  admin,
  BookCtrl.edit
);

/**
 * @api {delete} /books/:id Delete Book
 * @apiVersion 1.0.0
 * @apiName Delete Book
 * @apiGroup Book
 *
 * @apiHeader Authorization Bearer token
 *
 *
 */
router.delete(
  "/:id",
  Validation.validateDetailBook,
  validator,
  admin,
  BookCtrl.remove
);

/**
 * @api {get} /books Get Books
 * @apiVersion 1.0.0
 * @apiName  Get Books
 * @apiGroup Book
 *
 *
 */
router.get(
  "/",
  BookCtrl.list
);

module.exports = router;
