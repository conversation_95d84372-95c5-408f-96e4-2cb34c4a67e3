var mongoose = require("mongoose");
var Schema = mongoose.Schema;
import _ from "lodash";

var Book = new Schema(
  {
    name: { type: String, required: true },
    image: { type: String, required: true },
    chapters: [
      {
        title: { type: String, required: true },
        content: { type: String, required: true },
      }
    ]
  },
  {
    toObject: {
      transform: function (doc, ret, options) {
        delete ret.__v;
        return ret;
      },
    },
    toJSON: {
      transform: function (doc, ret, options) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

module.exports = mongoose.model("Book", Book);
