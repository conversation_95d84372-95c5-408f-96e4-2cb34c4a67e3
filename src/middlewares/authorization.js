import express from "express";
const router = express.Router();
import UserPackage from "../packages/user";
import AuthPackage from "../packages/auth";

router.use(async (req, res, next) => {
  // Check header for token
  const authorization = req.headers.authorization;
  if (authorization && authorization.indexOf("Bearer") > -1) {
    const token = authorization.replace("Bearer ", "");
    if (token && token != "null") {
      try {
        var decoded = AuthPackage.Service.decodeToken(token);
        req.user = await UserPackage.Service.getUserById(decoded.userId);
      } catch (err) {
        console.log(req.baseUrl);
        console.log(err);
      }
    }
  }
  next();
});

export default router;
