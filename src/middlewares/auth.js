import Constants from '../common/constants'
import { response } from '../common/utils'

module.exports = async (req, res, next) => {
    const user = req.user
    if (user && user.enabled) {
        req.userId = user._id
        req.userInfo = user
        try {
            next()
        } catch (error) {
            res.status(Constants.ApiStatusCodes.Unauthorized).send(response(false, null, error));
        }
    } else {
        res.status(Constants.ApiStatusCodes.Unauthorized).send(response(false, null, __('No Permission')));
    }
}