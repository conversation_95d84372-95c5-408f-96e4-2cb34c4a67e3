#!/bin/bash
# Install Nodejs
if which node > /dev/null
    then
        echo "node is installed, skipping..."
    else
       curl -sL https://rpm.nodesource.com/setup_14.x | sudo bash -
        sudo yum install -y nodejs
        yum install git -y
        sudo npm install -g yarn
        sudo yum install gcc-c++ make
        sudo npm install -g pm2
        echo "NodeJS installed successfully!!!"
    fi
# Install Nginx
if [ -e /etc/nginx/nginx.conf ]
    then
        echo "nginx is installed, skipping..."
    else
    sudo dnf install nginx
    sudo systemctl enable nginx
    sudo systemctl start nginx
    echo "Nginx installed successfully!!!"
fi
# Install mongodb
if [ ! -f /usr/bin/mongod ]
    then
        echo "[mongodb-org-5.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/redhat/$releasever/mongodb-org/5.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://www.mongodb.org/static/pgp/server-5.0.asc
" | sudo tee /etc/yum.repos.d/mongodb-org-5.0.repo
        sudo yum install -y mongodb-org
        sudo yum install -y mongodb-org-5.0.5 mongodb-org-database-5.0.5 mongodb-org-server-5.0.5 mongodb-org-shell-5.0.5 mongodb-org-mongos-5.0.5 mongodb-org-tools-5.0.5
        echo "Mongodb installed successfully!!!"
else
  echo "Mongo db already installed.  Skipping..."
fi
# Start server
yarn
if [ ! -d "public/images" ]
then
    sudo mkdir public/images
else
    echo "Images folder exists"
fi
if [ ! -d "public/images/temp" ]
then
    sudo mkdir public/images/temp
else
    echo "Images folder exists"
fi
pm2 start