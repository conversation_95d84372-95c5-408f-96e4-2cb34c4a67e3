/**
 * Migration script to add arNoDiacritics fields to existing ayahs and surahs
 * This script will update all existing records in the database
 */

require('@babel/register')({
  extends: './.babelrc'
})
require('babel-polyfill')
require('dotenv-flow').config()

import * as db from './src/common/db'
import Ayah from './src/packages/ayah/model'
import Surah from './src/packages/surah/model'
import { removeDiacritics } from './src/common/arabic-utils'

const migrateAyahs = async () => {
  try {
    console.log('🔄 Starting Ayahs migration...')
    
    // Get all ayahs from database
    const ayahs = await Ayah.find({})
    console.log(`📊 Found ${ayahs.length} ayahs to migrate`)
    
    let updatedCount = 0
    let skippedCount = 0
    
    for (const ayah of ayahs) {
      try {
        const updateData = {}
        let hasUpdates = false
        
        // Process title.ar
        if (ayah.title && ayah.title.ar && !ayah.title.arNoDiacritics) {
          if (!updateData['title.arNoDiacritics']) {
            updateData['title.arNoDiacritics'] = removeDiacritics(ayah.title.ar)
            hasUpdates = true
          }
        }
        
        // Process content.ar
        if (ayah.content && ayah.content.ar && !ayah.content.arNoDiacritics) {
          if (!updateData['content.arNoDiacritics']) {
            updateData['content.arNoDiacritics'] = removeDiacritics(ayah.content.ar)
            hasUpdates = true
          }
        }
        
        // Process explanation.ar
        if (ayah.explanation && ayah.explanation.ar && !ayah.explanation.arNoDiacritics) {
          if (!updateData['explanation.arNoDiacritics']) {
            updateData['explanation.arNoDiacritics'] = removeDiacritics(ayah.explanation.ar)
            hasUpdates = true
          }
        }
        
        if (hasUpdates) {
          await Ayah.updateOne({ _id: ayah._id }, { $set: updateData })
          updatedCount++
          
          if (updatedCount % 100 === 0) {
            console.log(`✅ Processed ${updatedCount} ayahs...`)
          }
        } else {
          skippedCount++
        }
        
      } catch (error) {
        console.error(`❌ Error processing ayah ${ayah.id}:`, error.message)
      }
    }
    
    console.log(`✅ Ayahs migration completed!`)
    console.log(`   - Updated: ${updatedCount} ayahs`)
    console.log(`   - Skipped: ${skippedCount} ayahs (already had arNoDiacritics)`)
    
  } catch (error) {
    console.error('❌ Error in ayahs migration:', error)
    throw error
  }
}

const migrateSurahs = async () => {
  try {
    console.log('\n🔄 Starting Surahs migration...')
    
    // Get all surahs from database
    const surahs = await Surah.find({})
    console.log(`📊 Found ${surahs.length} surahs to migrate`)
    
    let updatedCount = 0
    let skippedCount = 0
    
    for (const surah of surahs) {
      try {
        const updateData = {}
        let hasUpdates = false
        
        // Process title.ar
        if (surah.title && surah.title.ar && !surah.title.arNoDiacritics) {
          updateData['title.arNoDiacritics'] = removeDiacritics(surah.title.ar)
          hasUpdates = true
        }
        
        if (hasUpdates) {
          await Surah.updateOne({ _id: surah._id }, { $set: updateData })
          updatedCount++
        } else {
          skippedCount++
        }
        
      } catch (error) {
        console.error(`❌ Error processing surah ${surah.id}:`, error.message)
      }
    }
    
    console.log(`✅ Surahs migration completed!`)
    console.log(`   - Updated: ${updatedCount} surahs`)
    console.log(`   - Skipped: ${skippedCount} surahs (already had arNoDiacritics)`)
    
  } catch (error) {
    console.error('❌ Error in surahs migration:', error)
    throw error
  }
}

const runMigration = async () => {
  try {
    console.log('🚀 Starting Arabic Diacritics Migration')
    console.log('=====================================')
    
    // Connect to database
    console.log('🔌 Connecting to database...')
    await db.connect()
    console.log('✅ Database connected successfully')
    
    // Run migrations
    await migrateAyahs()
    await migrateSurahs()
    
    console.log('\n🎉 Migration completed successfully!')
    console.log('All existing records now have arNoDiacritics fields')
    
  } catch (error) {
    console.error('\n💥 Migration failed:', error)
    process.exit(1)
  } finally {
    // Close database connection
    console.log('\n🔌 Closing database connection...')
    process.exit(0)
  }
}

// Run the migration
runMigration()
