// Test script for Arabic diacritics removal functionality
// This demonstrates how the system handles Arabic text with and without diacritics

const {
  removeDiacritics,
  normalizeArabicText,
  containsArabic
} = require('./src/common/arabic-utils')

console.log('=== ARABIC DIACRITICS REMOVAL TEST ===\n')

// Test cases with Arabic text containing diacritics
const testCases = [
  {
    name: 'Al-Fat<PERSON><PERSON> (with diacritics)',
    original: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
    expected: 'بسم الله الرحمان الرحيم'
  },
  {
    name: '<PERSON>-<PERSON><PERSON><PERSON> (your example)',
    original: 'إِنَّآ أَعْطَيْنَـٰكَ ٱلْكَوْثَرَ',
    expected: 'انا اعطيناك الكوثر'
  },
  {
    name: 'Al-Baqarah verse (with diacritics)',
    original: 'الَّذِينَ يُؤْمِنُونَ بِالْغَيْبِ وَيُقِيمُونَ الصَّلَاةَ',
    expected: 'الذين يؤمنون بالغيب ويقيمون الصلاة'
  },
  {
    name: 'Mixed Arabic with punctuation',
    original: 'قُلْ هُوَ اللَّهُ أَحَدٌ، اللَّهُ الصَّمَدُ',
    expected: 'قل هو الله احد، الله الصمد'
  },
  {
    name: 'Different Alef forms',
    original: 'أَحْمَد إِبْرَاهِيم آدَم ٱلرَّحْمَن',
    expected: 'احمد ابراهيم ادم الرحمن'
  },
  {
    name: 'Text without diacritics',
    original: 'الله الرحمن الرحيم',
    expected: 'الله الرحمن الرحيم'
  }
]

console.log('Testing removeDiacritics function:')
console.log('=====================================')

testCases.forEach((testCase, index) => {
  const result = removeDiacritics(testCase.original)
  const passed = result === testCase.expected

  console.log(`\nTest ${index + 1}: ${testCase.name}`)
  console.log(`Original:  ${testCase.original}`)
  console.log(`Result:    ${result}`)
  console.log(`Expected:  ${testCase.expected}`)
  console.log(`Status:    ${passed ? '✅ PASSED' : '❌ FAILED'}`)
})

console.log('\n\nTesting normalizeArabicText function:')
console.log('=====================================')

const normalizationTests = [
  {
    name: 'Different Alef forms',
    original: 'أحمد إبراهيم آدم',
    expected: 'احمد ابراهيم ادم'
  },
  {
    name: 'Yeh and Alef Maksura',
    original: 'يحيى موسى',
    expected: 'يحيي موسي'
  },
  {
    name: 'Teh Marbuta',
    original: 'فاطمة خديجة',
    expected: 'فاطمه خديجه'
  }
]

normalizationTests.forEach((testCase, index) => {
  const result = normalizeArabicText(testCase.original)
  const passed = result === testCase.expected

  console.log(`\nTest ${index + 1}: ${testCase.name}`)
  console.log(`Original:  ${testCase.original}`)
  console.log(`Result:    ${result}`)
  console.log(`Expected:  ${testCase.expected}`)
  console.log(`Status:    ${passed ? '✅ PASSED' : '❌ FAILED'}`)
})

console.log('\n\nTesting containsArabic function:')
console.log('================================')

const arabicTests = [
  { text: 'بسم الله', expected: true },
  { text: 'Hello World', expected: false },
  { text: 'مرحبا Hello', expected: true },
  { text: '123456', expected: false },
  { text: '', expected: false }
]

arabicTests.forEach((testCase, index) => {
  const result = containsArabic(testCase.text)
  const passed = result === testCase.expected

  console.log(`Test ${index + 1}: "${testCase.text}" -> ${result} ${passed ? '✅' : '❌'}`)
})

console.log('\n\n=== HOW IT WORKS IN THE SYSTEM ===')
console.log('1. When saving an Ayah, the system automatically:')
console.log("   - Saves original Arabic text in 'ar' field")
console.log("   - Removes diacritics and saves in 'arNoDiacritics' field")
console.log('2. When searching, the system searches both fields:')
console.log("   - Original search term against 'ar' field")
console.log("   - Diacritics-removed search term against 'arNoDiacritics' field")
console.log('3. This ensures users can find content whether they type with or without diacritics')

console.log('\n=== EXAMPLE AYAH DATA STRUCTURE ===')
const exampleAyah = {
  id: 'A1001',
  title: {
    ar: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
    arNoDiacritics: 'بسم الله الرحمن الرحيم',
    sw: 'Kwa jina la Mwenyezi Mungu'
  },
  content: {
    ar: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
    arNoDiacritics: 'الحمد لله رب العالمين',
    sw: 'Sifa njema zote ni za Mwenyezi Mungu'
  }
}

console.log(JSON.stringify(exampleAyah, null, 2))
