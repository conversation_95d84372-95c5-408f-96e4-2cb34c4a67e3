{"name": "qurani-tufuku-server", "version": "1.0.0", "private": true, "apidoc": {"name": "<PERSON><PERSON>", "version": "1.0.0", "description": "Api document", "title": "Api document"}, "scripts": {"server": "nodemon app.js", "start": "NODE_ENV=localhost npm run server", "dev": "NODE_ENV=development npm run server", "prod": "NODE_ENV=production npm run server", "apidoc": "apidoc -i src/routes/  -o apidoc/", "migrate:arabic": "node migrate_arabic_diacritics.js", "verify:migration": "node verify_migration.js"}, "dependencies": {"apn": "^2.2.0", "apr-map": "^3.0.3", "aws-sdk": "^2.1073.0", "axios": "^0.19.2", "bcrypt": "^5.1.0", "body-parser": "^1.19.0", "compress-images": "^2.0.4", "cookie-parser": "~1.4.3", "cors": "^2.8.5", "cron": "^2.1.0", "debug": "~2.6.9", "dotenv-flow": "^3.1.0", "express": "~4.16.0", "express-validator": "^6.3.1", "i18n": "^0.8.3", "jimp": "^0.16.1", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.11", "moment": "^2.24.0", "mongoose": "^5.5.12", "multer": "^1.4.1", "nodemailer": "^6.7.2", "nodemon": "^1.19.1", "pug": "2.0.0-beta11", "randomstring": "^1.2.2"}, "devDependencies": {"@babel/cli": "7.8.3", "@babel/core": "7.8.3", "@babel/preset-env": "^7.9.0", "@babel/register": "7.8.3", "babel-polyfill": "^6.26.0"}}