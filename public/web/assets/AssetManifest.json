{"assets/fonts/Roboto-Bold.ttf": ["assets/fonts/Roboto-Bold.ttf"], "assets/fonts/Roboto-Italic.ttf": ["assets/fonts/Roboto-Italic.ttf"], "assets/fonts/Roboto-Light.ttf": ["assets/fonts/Roboto-Light.ttf"], "assets/fonts/Roboto-Regular.ttf": ["assets/fonts/Roboto-Regular.ttf"], "assets/fonts/Roboto-Thin.ttf": ["assets/fonts/Roboto-Thin.ttf"], "assets/icons/ic_add.svg": ["assets/icons/ic_add.svg"], "assets/icons/ic_attach.svg": ["assets/icons/ic_attach.svg"], "assets/icons/ic_circle_add.svg": ["assets/icons/ic_circle_add.svg"], "assets/icons/tabbar/ic_cart.svg": ["assets/icons/tabbar/ic_cart.svg"], "assets/icons/tabbar/ic_dashboard.svg": ["assets/icons/tabbar/ic_dashboard.svg"], "assets/icons/tabbar/ic_logout.svg": ["assets/icons/tabbar/ic_logout.svg"], "assets/icons/tabbar/ic_products.svg": ["assets/icons/tabbar/ic_products.svg"], "assets/icons/tabbar/ic_settings.svg": ["assets/icons/tabbar/ic_settings.svg"], "assets/icons/tabbar/ic_users.svg": ["assets/icons/tabbar/ic_users.svg"], "assets/icons/tabbar/money.svg": ["assets/icons/tabbar/money.svg"], "assets/images/app_icon.png": ["assets/images/app_icon.png"], "assets/images/default.jpeg": ["assets/images/default.jpeg"], "assets/images/empty.png": ["assets/images/empty.png"], "assets/images/logo.png": ["assets/images/logo.png"], "assets/images/printer_logo.png": ["assets/images/printer_logo.png"], "assets/images/refund.png": ["assets/images/refund.png"], "packages/cupertino_icons/assets/CupertinoIcons.ttf": ["packages/cupertino_icons/assets/CupertinoIcons.ttf"], "packages/esc_pos_utils/resources/capabilities.json": ["packages/esc_pos_utils/resources/capabilities.json"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_AMS-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_AMS-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Caligraphic-Bold.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Caligraphic-Bold.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Caligraphic-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Caligraphic-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Fraktur-Bold.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Fraktur-Bold.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Fraktur-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Fraktur-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Main-Bold.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Main-Bold.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Main-BoldItalic.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Main-BoldItalic.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Main-Italic.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Main-Italic.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Main-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Main-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Math-BoldItalic.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Math-BoldItalic.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Math-Italic.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Math-Italic.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_SansSerif-Bold.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_SansSerif-Bold.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_SansSerif-Italic.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_SansSerif-Italic.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_SansSerif-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_SansSerif-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Script-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Script-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Size1-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Size1-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Size2-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Size2-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Size3-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Size3-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Size4-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Size4-Regular.ttf"], "packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Typewriter-Regular.ttf": ["packages/flutter_math_fork/lib/katex_fonts/fonts/KaTeX_Typewriter-Regular.ttf"], "packages/wakelock_web/assets/no_sleep.js": ["packages/wakelock_web/assets/no_sleep.js"]}