
var CanvasKitInit = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(CanvasKitInit) {
  CanvasKitInit = CanvasKitInit || {};


null;var v;v||(v=typeof CanvasKitInit !== 'undefined' ? CanvasKitInit : {});var da,fa;v.ready=new Promise(function(a,b){da=a;fa=b});
(function(a){a.Wd=a.Wd||[];a.Wd.push(function(){a.MakeSWCanvasSurface=function(b){var c=b;if("CANVAS"!==c.tagName&&(c=document.getElementById(b),!c))throw"Canvas with id "+b+" was not found";if(b=a.MakeSurface(c.width,c.height))b.Od=c;return b};a.MakeCanvasSurface||(a.MakeCanvasSurface=a.MakeSWCanvasSurface);a.MakeSurface=function(b,c){var f={width:b,height:c,colorType:a.ColorType.RGBA_8888,alphaType:a.AlphaType.Unpremul,colorSpace:a.ColorSpace.SRGB},h=b*c*4,m=a._malloc(h);if(f=a.Surface._makeRasterDirect(f,
m,4*b))f.Od=null,f.Ff=b,f.Bf=c,f.Df=h,f.af=m,f.getCanvas().clear(a.TRANSPARENT);return f};a.MakeRasterDirectSurface=function(b,c,f){return a.Surface._makeRasterDirect(b,c.byteOffset,f)};a.Surface.prototype.flush=function(b){a.Pd(this.Nd);this._flush();if(this.Od){var c=new Uint8ClampedArray(a.HEAPU8.buffer,this.af,this.Df);c=new ImageData(c,this.Ff,this.Bf);b?this.Od.getContext("2d").putImageData(c,0,0,b[0],b[1],b[2]-b[0],b[3]-b[1]):this.Od.getContext("2d").putImageData(c,0,0)}};a.Surface.prototype.dispose=
function(){this.af&&a._free(this.af);this.delete()};a.Pd=a.Pd||function(){}})})(v);
(function(a){a.Wd=a.Wd||[];a.Wd.push(function(){function b(n,q,x){return n&&n.hasOwnProperty(q)?n[q]:x}function c(n){var q=ha(ka);ka[q]=n;return q}function f(n){return n.naturalHeight||n.videoHeight||n.displayHeight||n.height}function h(n){return n.naturalWidth||n.videoWidth||n.displayWidth||n.width}function m(n,q,x,C){n.bindTexture(n.TEXTURE_2D,q);C||x.alphaType!==a.AlphaType.Premul||n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0);return q}function t(n,q,x){x||q.alphaType!==a.AlphaType.Premul||
n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1);n.bindTexture(n.TEXTURE_2D,null)}a.GetWebGLContext=function(n,q){if(!n)throw"null canvas passed into makeWebGLContext";var x={alpha:b(q,"alpha",1),depth:b(q,"depth",1),stencil:b(q,"stencil",8),antialias:b(q,"antialias",0),premultipliedAlpha:b(q,"premultipliedAlpha",1),preserveDrawingBuffer:b(q,"preserveDrawingBuffer",0),preferLowPowerToHighPerformance:b(q,"preferLowPowerToHighPerformance",0),failIfMajorPerformanceCaveat:b(q,"failIfMajorPerformanceCaveat",
0),enableExtensionsByDefault:b(q,"enableExtensionsByDefault",1),explicitSwapControl:b(q,"explicitSwapControl",0),renderViaOffscreenBackBuffer:b(q,"renderViaOffscreenBackBuffer",0)};x.majorVersion=q&&q.majorVersion?q.majorVersion:"undefined"!==typeof WebGL2RenderingContext?2:1;if(x.explicitSwapControl)throw"explicitSwapControl is not supported";n=la(n,x);if(!n)return 0;ma(n);return n};a.deleteContext=function(n){w===na[n]&&(w=null);"object"==typeof JSEvents&&JSEvents.Ag(na[n].ke.canvas);na[n]&&na[n].ke.canvas&&
(na[n].ke.canvas.yf=void 0);na[n]=null};a._setTextureCleanup({deleteTexture:function(n,q){var x=ka[q];x&&na[n].ke.deleteTexture(x);ka[q]=null}});a.zf=function(n){if(!this.Pd(n))return null;var q=this._MakeGrContext();if(!q)return null;q.Nd=n;return q};a.MakeGrContext=a.zf;a.MakeOnScreenGLSurface=function(n,q,x,C){if(!this.Pd(n.Nd))return null;q=this._MakeOnScreenGLSurface(n,q,x,C);if(!q)return null;q.Nd=n.Nd;return q};a.MakeRenderTarget=function(){var n=arguments[0];if(!this.Pd(n.Nd))return null;
if(3===arguments.length){var q=this._MakeRenderTargetWH(n,arguments[1],arguments[2]);if(!q)return null}else if(2===arguments.length){if(q=this._MakeRenderTargetII(n,arguments[1]),!q)return null}else return null;q.Nd=n.Nd;return q};a.MakeWebGLCanvasSurface=function(n,q,x){q=q||null;var C=n,H="undefined"!==typeof OffscreenCanvas&&C instanceof OffscreenCanvas;if(!("undefined"!==typeof HTMLCanvasElement&&C instanceof HTMLCanvasElement||H||(C=document.getElementById(n),C)))throw"Canvas with id "+n+" was not found";
n=this.GetWebGLContext(C,x);if(!n||0>n)throw"failed to create webgl context: err "+n;n=this.MakeGrContext(n);q=this.MakeOnScreenGLSurface(n,C.width,C.height,q);return q?q:(q=C.cloneNode(!0),C.parentNode.replaceChild(q,C),q.classList.add("ck-replaced"),a.MakeSWCanvasSurface(q))};a.MakeCanvasSurface=a.MakeWebGLCanvasSurface;a.Surface.prototype.makeImageFromTexture=function(n,q){a.Pd(this.Nd);n=c(n);if(q=this._makeImageFromTexture(this.Nd,n,q))q.Me=n;return q};a.Surface.prototype.makeImageFromTextureSource=
function(n,q,x){q||(q={height:f(n),width:h(n),colorType:a.ColorType.RGBA_8888,alphaType:x?a.AlphaType.Premul:a.AlphaType.Unpremul});q.colorSpace||(q.colorSpace=a.ColorSpace.SRGB);a.Pd(this.Nd);var C=w.ke;x=m(C,C.createTexture(),q,x);2===w.version?C.texImage2D(C.TEXTURE_2D,0,C.RGBA,q.width,q.height,0,C.RGBA,C.UNSIGNED_BYTE,n):C.texImage2D(C.TEXTURE_2D,0,C.RGBA,C.RGBA,C.UNSIGNED_BYTE,n);t(C,q);return this.makeImageFromTexture(x,q)};a.Surface.prototype.updateTextureFromSource=function(n,q,x){if(n.Me){a.Pd(this.Nd);
var C=n.getImageInfo(),H=w.ke,M=m(H,ka[n.Me],C,x);2===w.version?H.texImage2D(H.TEXTURE_2D,0,H.RGBA,h(q),f(q),0,H.RGBA,H.UNSIGNED_BYTE,q):H.texImage2D(H.TEXTURE_2D,0,H.RGBA,H.RGBA,H.UNSIGNED_BYTE,q);t(H,C,x);this._resetContext();ka[n.Me]=null;n.Me=c(M);C.colorSpace=n.getColorSpace();q=this._makeImageFromTexture(this.Nd,n.Me,C);x=n.Md.Ud;H=n.Md.ae;n.Md.Ud=q.Md.Ud;n.Md.ae=q.Md.ae;q.Md.Ud=x;q.Md.ae=H;q.delete();C.colorSpace.delete()}};a.MakeLazyImageFromTextureSource=function(n,q,x){q||(q={height:f(n),
width:h(n),colorType:a.ColorType.RGBA_8888,alphaType:x?a.AlphaType.Premul:a.AlphaType.Unpremul});q.colorSpace||(q.colorSpace=a.ColorSpace.SRGB);var C={makeTexture:function(){var H=w,M=H.ke,A=m(M,M.createTexture(),q,x);2===H.version?M.texImage2D(M.TEXTURE_2D,0,M.RGBA,q.width,q.height,0,M.RGBA,M.UNSIGNED_BYTE,n):M.texImage2D(M.TEXTURE_2D,0,M.RGBA,M.RGBA,M.UNSIGNED_BYTE,n);t(M,q,x);return c(A)},freeSrc:function(){}};"VideoFrame"===n.constructor.name&&(C.freeSrc=function(){n.close()});return a.Image._makeFromGenerator(q,
C)};a.Pd=function(n){return n?ma(n):!1}})})(v);
(function(a){function b(e,d,g,l,r){for(var y=0;y<e.length;y++)d[y*g+(y*r+l+g)%g]=e[y];return d}function c(e){for(var d=e*e,g=Array(d);d--;)g[d]=0===d%(e+1)?1:0;return g}function f(e){return e?e.constructor===Float32Array&&4===e.length:!1}function h(e){return(n(255*e[3])<<24|n(255*e[0])<<16|n(255*e[1])<<8|n(255*e[2])<<0)>>>0}function m(e){if(e&&e._ck)return e;if(e instanceof Float32Array){for(var d=Math.floor(e.length/4),g=new Uint32Array(d),l=0;l<d;l++)g[l]=h(e.slice(4*l,4*(l+1)));return g}if(e instanceof
Uint32Array)return e;if(e instanceof Array&&e[0]instanceof Float32Array)return e.map(h)}function t(e){if(void 0===e)return 1;var d=parseFloat(e);return e&&-1!==e.indexOf("%")?d/100:d}function n(e){return Math.round(Math.max(0,Math.min(e||0,255)))}function q(e,d){d&&d._ck||a._free(e)}function x(e,d,g){if(!e||!e.length)return V;if(e&&e._ck)return e.byteOffset;var l=a[d].BYTES_PER_ELEMENT;g||(g=a._malloc(e.length*l));a[d].set(e,g/l);return g}function C(e){var d={de:V,count:e.length,colorType:a.ColorType.RGBA_F32};
if(e instanceof Float32Array)d.de=x(e,"HEAPF32"),d.count=e.length/4;else if(e instanceof Uint32Array)d.de=x(e,"HEAPU32"),d.colorType=a.ColorType.RGBA_8888;else if(e instanceof Array){if(e&&e.length){for(var g=a._malloc(16*e.length),l=0,r=g/4,y=0;y<e.length;y++)for(var E=0;4>E;E++)a.HEAPF32[r+l]=e[y][E],l++;e=g}else e=V;d.de=e}else throw"Invalid argument to copyFlexibleColorArray, Not a color array "+typeof e;return d}function H(e){if(!e)return V;var d=Vb.toTypedArray();if(e.length){if(6===e.length||
9===e.length)return x(e,"HEAPF32",Oa),6===e.length&&a.HEAPF32.set(zd,6+Oa/4),Oa;if(16===e.length)return d[0]=e[0],d[1]=e[1],d[2]=e[3],d[3]=e[4],d[4]=e[5],d[5]=e[7],d[6]=e[12],d[7]=e[13],d[8]=e[15],Oa;throw"invalid matrix size";}if(void 0===e.m11)throw"invalid matrix argument";d[0]=e.m11;d[1]=e.m21;d[2]=e.m41;d[3]=e.m12;d[4]=e.m22;d[5]=e.m42;d[6]=e.m14;d[7]=e.m24;d[8]=e.m44;return Oa}function M(e){if(!e)return V;var d=Wb.toTypedArray();if(e.length){if(16!==e.length&&6!==e.length&&9!==e.length)throw"invalid matrix size";
if(16===e.length)return x(e,"HEAPF32",$a);d.fill(0);d[0]=e[0];d[1]=e[1];d[3]=e[2];d[4]=e[3];d[5]=e[4];d[7]=e[5];d[10]=1;d[12]=e[6];d[13]=e[7];d[15]=e[8];6===e.length&&(d[12]=0,d[13]=0,d[15]=1);return $a}if(void 0===e.m11)throw"invalid matrix argument";d[0]=e.m11;d[1]=e.m21;d[2]=e.m31;d[3]=e.m41;d[4]=e.m12;d[5]=e.m22;d[6]=e.m32;d[7]=e.m42;d[8]=e.m13;d[9]=e.m23;d[10]=e.m33;d[11]=e.m43;d[12]=e.m14;d[13]=e.m24;d[14]=e.m34;d[15]=e.m44;return $a}function A(e,d){return x(e,"HEAPF32",d||fb)}function N(e,
d,g,l){var r=Xb.toTypedArray();r[0]=e;r[1]=d;r[2]=g;r[3]=l;return fb}function S(e){for(var d=new Float32Array(4),g=0;4>g;g++)d[g]=a.HEAPF32[e/4+g];return d}function T(e,d){return x(e,"HEAPF32",d||ia)}function pa(e,d){return x(e,"HEAPF32",d||Yb)}function ta(){for(var e=0,d=0;d<arguments.length-1;d+=2)e+=arguments[d]*arguments[d+1];return e}function gb(e,d,g){for(var l=Array(e.length),r=0;r<g;r++)for(var y=0;y<g;y++){for(var E=0,K=0;K<g;K++)E+=e[g*r+K]*d[g*K+y];l[r*g+y]=E}return l}function hb(e,d){for(var g=
gb(d[0],d[1],e),l=2;l<d.length;)g=gb(g,d[l],e),l++;return g}a.Color=function(e,d,g,l){void 0===l&&(l=1);return a.Color4f(n(e)/255,n(d)/255,n(g)/255,l)};a.ColorAsInt=function(e,d,g,l){void 0===l&&(l=255);return(n(l)<<24|n(e)<<16|n(d)<<8|n(g)<<0&268435455)>>>0};a.Color4f=function(e,d,g,l){void 0===l&&(l=1);return Float32Array.of(e,d,g,l)};Object.defineProperty(a,"TRANSPARENT",{get:function(){return a.Color4f(0,0,0,0)}});Object.defineProperty(a,"BLACK",{get:function(){return a.Color4f(0,0,0,1)}});Object.defineProperty(a,
"WHITE",{get:function(){return a.Color4f(1,1,1,1)}});Object.defineProperty(a,"RED",{get:function(){return a.Color4f(1,0,0,1)}});Object.defineProperty(a,"GREEN",{get:function(){return a.Color4f(0,1,0,1)}});Object.defineProperty(a,"BLUE",{get:function(){return a.Color4f(0,0,1,1)}});Object.defineProperty(a,"YELLOW",{get:function(){return a.Color4f(1,1,0,1)}});Object.defineProperty(a,"CYAN",{get:function(){return a.Color4f(0,1,1,1)}});Object.defineProperty(a,"MAGENTA",{get:function(){return a.Color4f(1,
0,1,1)}});a.getColorComponents=function(e){return[Math.floor(255*e[0]),Math.floor(255*e[1]),Math.floor(255*e[2]),e[3]]};a.parseColorString=function(e,d){e=e.toLowerCase();if(e.startsWith("#")){d=255;switch(e.length){case 9:d=parseInt(e.slice(7,9),16);case 7:var g=parseInt(e.slice(1,3),16);var l=parseInt(e.slice(3,5),16);var r=parseInt(e.slice(5,7),16);break;case 5:d=17*parseInt(e.slice(4,5),16);case 4:g=17*parseInt(e.slice(1,2),16),l=17*parseInt(e.slice(2,3),16),r=17*parseInt(e.slice(3,4),16)}return a.Color(g,
l,r,d/255)}return e.startsWith("rgba")?(e=e.slice(5,-1),e=e.split(","),a.Color(+e[0],+e[1],+e[2],t(e[3]))):e.startsWith("rgb")?(e=e.slice(4,-1),e=e.split(","),a.Color(+e[0],+e[1],+e[2],t(e[3]))):e.startsWith("gray(")||e.startsWith("hsl")||!d||(e=d[e],void 0===e)?a.BLACK:e};a.multiplyByAlpha=function(e,d){e=e.slice();e[3]=Math.max(0,Math.min(e[3]*d,1));return e};a.Malloc=function(e,d){var g=a._malloc(d*e.BYTES_PER_ELEMENT);return{_ck:!0,length:d,byteOffset:g,se:null,subarray:function(l,r){l=this.toTypedArray().subarray(l,
r);l._ck=!0;return l},toTypedArray:function(){if(this.se&&this.se.length)return this.se;this.se=new e(a.HEAPU8.buffer,g,d);this.se._ck=!0;return this.se}}};a.Free=function(e){a._free(e.byteOffset);e.byteOffset=V;e.toTypedArray=null;e.se=null};var Oa=V,Vb,$a=V,Wb,fb=V,Xb,Ha,ia=V,Cc,Ta=V,Dc,Zb=V,Ec,$b=V,ac,xb=V,Fc,Yb=V,Gc,Hc=V,zd=Float32Array.of(0,0,1),V=0;a.onRuntimeInitialized=function(){function e(d,g,l,r,y,E){E||(E=4*r.width,r.colorType===a.ColorType.RGBA_F16?E*=2:r.colorType===a.ColorType.RGBA_F32&&
(E*=4));var K=E*r.height;var O=y?y.byteOffset:a._malloc(K);if(!d._readPixels(r,O,E,g,l))return y||a._free(O),null;if(y)return y.toTypedArray();switch(r.colorType){case a.ColorType.RGBA_8888:case a.ColorType.RGBA_F16:d=(new Uint8Array(a.HEAPU8.buffer,O,K)).slice();break;case a.ColorType.RGBA_F32:d=(new Float32Array(a.HEAPU8.buffer,O,K)).slice();break;default:return null}a._free(O);return d}Xb=a.Malloc(Float32Array,4);fb=Xb.byteOffset;Wb=a.Malloc(Float32Array,16);$a=Wb.byteOffset;Vb=a.Malloc(Float32Array,
9);Oa=Vb.byteOffset;Fc=a.Malloc(Float32Array,12);Yb=Fc.byteOffset;Gc=a.Malloc(Float32Array,12);Hc=Gc.byteOffset;Ha=a.Malloc(Float32Array,4);ia=Ha.byteOffset;Cc=a.Malloc(Float32Array,4);Ta=Cc.byteOffset;Dc=a.Malloc(Float32Array,3);Zb=Dc.byteOffset;Ec=a.Malloc(Float32Array,3);$b=Ec.byteOffset;ac=a.Malloc(Int32Array,4);xb=ac.byteOffset;a.ColorSpace.SRGB=a.ColorSpace._MakeSRGB();a.ColorSpace.DISPLAY_P3=a.ColorSpace._MakeDisplayP3();a.ColorSpace.ADOBE_RGB=a.ColorSpace._MakeAdobeRGB();a.GlyphRunFlags={IsWhiteSpace:a._GlyphRunFlags_isWhiteSpace};
a.Path.MakeFromCmds=function(d){var g=x(d,"HEAPF32"),l=a.Path._MakeFromCmds(g,d.length);q(g,d);return l};a.Path.MakeFromVerbsPointsWeights=function(d,g,l){var r=x(d,"HEAPU8"),y=x(g,"HEAPF32"),E=x(l,"HEAPF32"),K=a.Path._MakeFromVerbsPointsWeights(r,d.length,y,g.length,E,l&&l.length||0);q(r,d);q(y,g);q(E,l);return K};a.Path.prototype.addArc=function(d,g,l){d=T(d);this._addArc(d,g,l);return this};a.Path.prototype.addOval=function(d,g,l){void 0===l&&(l=1);d=T(d);this._addOval(d,!!g,l);return this};a.Path.prototype.addPath=
function(){var d=Array.prototype.slice.call(arguments),g=d[0],l=!1;"boolean"===typeof d[d.length-1]&&(l=d.pop());if(1===d.length)this._addPath(g,1,0,0,0,1,0,0,0,1,l);else if(2===d.length)d=d[1],this._addPath(g,d[0],d[1],d[2],d[3],d[4],d[5],d[6]||0,d[7]||0,d[8]||1,l);else if(7===d.length||10===d.length)this._addPath(g,d[1],d[2],d[3],d[4],d[5],d[6],d[7]||0,d[8]||0,d[9]||1,l);else return null;return this};a.Path.prototype.addPoly=function(d,g){var l=x(d,"HEAPF32");this._addPoly(l,d.length/2,g);q(l,d);
return this};a.Path.prototype.addRect=function(d,g){d=T(d);this._addRect(d,!!g);return this};a.Path.prototype.addRRect=function(d,g){d=pa(d);this._addRRect(d,!!g);return this};a.Path.prototype.addVerbsPointsWeights=function(d,g,l){var r=x(d,"HEAPU8"),y=x(g,"HEAPF32"),E=x(l,"HEAPF32");this._addVerbsPointsWeights(r,d.length,y,g.length,E,l&&l.length||0);q(r,d);q(y,g);q(E,l)};a.Path.prototype.arc=function(d,g,l,r,y,E){d=a.LTRBRect(d-l,g-l,d+l,g+l);y=(y-r)/Math.PI*180-360*!!E;E=new a.Path;E.addArc(d,r/
Math.PI*180,y);this.addPath(E,!0);E.delete();return this};a.Path.prototype.arcToOval=function(d,g,l,r){d=T(d);this._arcToOval(d,g,l,r);return this};a.Path.prototype.arcToRotated=function(d,g,l,r,y,E,K){this._arcToRotated(d,g,l,!!r,!!y,E,K);return this};a.Path.prototype.arcToTangent=function(d,g,l,r,y){this._arcToTangent(d,g,l,r,y);return this};a.Path.prototype.close=function(){this._close();return this};a.Path.prototype.conicTo=function(d,g,l,r,y){this._conicTo(d,g,l,r,y);return this};a.Path.prototype.computeTightBounds=
function(d){this._computeTightBounds(ia);var g=Ha.toTypedArray();return d?(d.set(g),d):g.slice()};a.Path.prototype.cubicTo=function(d,g,l,r,y,E){this._cubicTo(d,g,l,r,y,E);return this};a.Path.prototype.dash=function(d,g,l){return this._dash(d,g,l)?this:null};a.Path.prototype.getBounds=function(d){this._getBounds(ia);var g=Ha.toTypedArray();return d?(d.set(g),d):g.slice()};a.Path.prototype.lineTo=function(d,g){this._lineTo(d,g);return this};a.Path.prototype.moveTo=function(d,g){this._moveTo(d,g);return this};
a.Path.prototype.offset=function(d,g){this._transform(1,0,d,0,1,g,0,0,1);return this};a.Path.prototype.quadTo=function(d,g,l,r){this._quadTo(d,g,l,r);return this};a.Path.prototype.rArcTo=function(d,g,l,r,y,E,K){this._rArcTo(d,g,l,r,y,E,K);return this};a.Path.prototype.rConicTo=function(d,g,l,r,y){this._rConicTo(d,g,l,r,y);return this};a.Path.prototype.rCubicTo=function(d,g,l,r,y,E){this._rCubicTo(d,g,l,r,y,E);return this};a.Path.prototype.rLineTo=function(d,g){this._rLineTo(d,g);return this};a.Path.prototype.rMoveTo=
function(d,g){this._rMoveTo(d,g);return this};a.Path.prototype.rQuadTo=function(d,g,l,r){this._rQuadTo(d,g,l,r);return this};a.Path.prototype.stroke=function(d){d=d||{};d.width=d.width||1;d.miter_limit=d.miter_limit||4;d.cap=d.cap||a.StrokeCap.Butt;d.join=d.join||a.StrokeJoin.Miter;d.precision=d.precision||1;return this._stroke(d)?this:null};a.Path.prototype.transform=function(){if(1===arguments.length){var d=arguments[0];this._transform(d[0],d[1],d[2],d[3],d[4],d[5],d[6]||0,d[7]||0,d[8]||1)}else if(6===
arguments.length||9===arguments.length)d=arguments,this._transform(d[0],d[1],d[2],d[3],d[4],d[5],d[6]||0,d[7]||0,d[8]||1);else throw"transform expected to take 1 or 9 arguments. Got "+arguments.length;return this};a.Path.prototype.trim=function(d,g,l){return this._trim(d,g,!!l)?this:null};a.Image.prototype.makeShaderCubic=function(d,g,l,r,y){y=H(y);return this._makeShaderCubic(d,g,l,r,y)};a.Image.prototype.makeShaderOptions=function(d,g,l,r,y){y=H(y);return this._makeShaderOptions(d,g,l,r,y)};a.Image.prototype.readPixels=
function(d,g,l,r,y){return e(this,d,g,l,r,y)};a.Canvas.prototype.clear=function(d){a.Pd(this.Nd);d=A(d);this._clear(d)};a.Canvas.prototype.clipRRect=function(d,g,l){a.Pd(this.Nd);d=pa(d);this._clipRRect(d,g,l)};a.Canvas.prototype.clipRect=function(d,g,l){a.Pd(this.Nd);d=T(d);this._clipRect(d,g,l)};a.Canvas.prototype.concat=function(d){a.Pd(this.Nd);d=M(d);this._concat(d)};a.Canvas.prototype.drawArc=function(d,g,l,r,y){a.Pd(this.Nd);d=T(d);this._drawArc(d,g,l,r,y)};a.Canvas.prototype.drawAtlas=function(d,
g,l,r,y,E,K){if(d&&r&&g&&l&&g.length===l.length){a.Pd(this.Nd);y||(y=a.BlendMode.SrcOver);var O=x(g,"HEAPF32"),Q=x(l,"HEAPF32"),W=l.length/4,u=x(m(E),"HEAPU32");if(K&&"B"in K&&"C"in K)this._drawAtlasCubic(d,Q,O,u,W,y,K.B,K.C,r);else{let I=a.FilterMode.Linear,R=a.MipmapMode.None;K&&(I=K.filter,"mipmap"in K&&(R=K.mipmap));this._drawAtlasOptions(d,Q,O,u,W,y,I,R,r)}q(O,g);q(Q,l);q(u,E)}};a.Canvas.prototype.drawCircle=function(d,g,l,r){a.Pd(this.Nd);this._drawCircle(d,g,l,r)};a.Canvas.prototype.drawColor=
function(d,g){a.Pd(this.Nd);d=A(d);void 0!==g?this._drawColor(d,g):this._drawColor(d)};a.Canvas.prototype.drawColorInt=function(d,g){a.Pd(this.Nd);this._drawColorInt(d,g||a.BlendMode.SrcOver)};a.Canvas.prototype.drawColorComponents=function(d,g,l,r,y){a.Pd(this.Nd);d=N(d,g,l,r);void 0!==y?this._drawColor(d,y):this._drawColor(d)};a.Canvas.prototype.drawDRRect=function(d,g,l){a.Pd(this.Nd);d=pa(d,Yb);g=pa(g,Hc);this._drawDRRect(d,g,l)};a.Canvas.prototype.drawImage=function(d,g,l,r){a.Pd(this.Nd);this._drawImage(d,
g,l,r||null)};a.Canvas.prototype.drawImageCubic=function(d,g,l,r,y,E){a.Pd(this.Nd);this._drawImageCubic(d,g,l,r,y,E||null)};a.Canvas.prototype.drawImageOptions=function(d,g,l,r,y,E){a.Pd(this.Nd);this._drawImageOptions(d,g,l,r,y,E||null)};a.Canvas.prototype.drawImageNine=function(d,g,l,r,y){a.Pd(this.Nd);g=x(g,"HEAP32",xb);l=T(l);this._drawImageNine(d,g,l,r,y||null)};a.Canvas.prototype.drawImageRect=function(d,g,l,r,y){a.Pd(this.Nd);T(g,ia);T(l,Ta);this._drawImageRect(d,ia,Ta,r,!!y)};a.Canvas.prototype.drawImageRectCubic=
function(d,g,l,r,y,E){a.Pd(this.Nd);T(g,ia);T(l,Ta);this._drawImageRectCubic(d,ia,Ta,r,y,E||null)};a.Canvas.prototype.drawImageRectOptions=function(d,g,l,r,y,E){a.Pd(this.Nd);T(g,ia);T(l,Ta);this._drawImageRectOptions(d,ia,Ta,r,y,E||null)};a.Canvas.prototype.drawLine=function(d,g,l,r,y){a.Pd(this.Nd);this._drawLine(d,g,l,r,y)};a.Canvas.prototype.drawOval=function(d,g){a.Pd(this.Nd);d=T(d);this._drawOval(d,g)};a.Canvas.prototype.drawPaint=function(d){a.Pd(this.Nd);this._drawPaint(d)};a.Canvas.prototype.drawParagraph=
function(d,g,l){a.Pd(this.Nd);this._drawParagraph(d,g,l)};a.Canvas.prototype.drawPatch=function(d,g,l,r,y){if(24>d.length)throw"Need 12 cubic points";if(g&&4>g.length)throw"Need 4 colors";if(l&&8>l.length)throw"Need 4 shader coordinates";a.Pd(this.Nd);const E=x(d,"HEAPF32"),K=g?x(m(g),"HEAPU32"):V,O=l?x(l,"HEAPF32"):V;r||(r=a.BlendMode.Modulate);this._drawPatch(E,K,O,r,y);q(O,l);q(K,g);q(E,d)};a.Canvas.prototype.drawPath=function(d,g){a.Pd(this.Nd);this._drawPath(d,g)};a.Canvas.prototype.drawPicture=
function(d){a.Pd(this.Nd);this._drawPicture(d)};a.Canvas.prototype.drawPoints=function(d,g,l){a.Pd(this.Nd);var r=x(g,"HEAPF32");this._drawPoints(d,r,g.length/2,l);q(r,g)};a.Canvas.prototype.drawRRect=function(d,g){a.Pd(this.Nd);d=pa(d);this._drawRRect(d,g)};a.Canvas.prototype.drawRect=function(d,g){a.Pd(this.Nd);d=T(d);this._drawRect(d,g)};a.Canvas.prototype.drawRect4f=function(d,g,l,r,y){a.Pd(this.Nd);this._drawRect4f(d,g,l,r,y)};a.Canvas.prototype.drawShadow=function(d,g,l,r,y,E,K){a.Pd(this.Nd);
var O=x(y,"HEAPF32"),Q=x(E,"HEAPF32");g=x(g,"HEAPF32",Zb);l=x(l,"HEAPF32",$b);this._drawShadow(d,g,l,r,O,Q,K);q(O,y);q(Q,E)};a.getShadowLocalBounds=function(d,g,l,r,y,E,K){d=H(d);l=x(l,"HEAPF32",Zb);r=x(r,"HEAPF32",$b);if(!this._getShadowLocalBounds(d,g,l,r,y,E,ia))return null;g=Ha.toTypedArray();return K?(K.set(g),K):g.slice()};a.Canvas.prototype.drawTextBlob=function(d,g,l,r){a.Pd(this.Nd);this._drawTextBlob(d,g,l,r)};a.Canvas.prototype.drawVertices=function(d,g,l){a.Pd(this.Nd);this._drawVertices(d,
g,l)};a.Canvas.prototype.getDeviceClipBounds=function(d){this._getDeviceClipBounds(xb);var g=ac.toTypedArray();d?d.set(g):d=g.slice();return d};a.Canvas.prototype.getLocalToDevice=function(){this._getLocalToDevice($a);for(var d=$a,g=Array(16),l=0;16>l;l++)g[l]=a.HEAPF32[d/4+l];return g};a.Canvas.prototype.getTotalMatrix=function(){this._getTotalMatrix(Oa);for(var d=Array(9),g=0;9>g;g++)d[g]=a.HEAPF32[Oa/4+g];return d};a.Canvas.prototype.makeSurface=function(d){d=this._makeSurface(d);d.Nd=this.Nd;
return d};a.Canvas.prototype.readPixels=function(d,g,l,r,y){a.Pd(this.Nd);return e(this,d,g,l,r,y)};a.Canvas.prototype.saveLayer=function(d,g,l,r){g=T(g);return this._saveLayer(d||null,g,l||null,r||0)};a.Canvas.prototype.writePixels=function(d,g,l,r,y,E,K,O){if(d.byteLength%(g*l))throw"pixels length must be a multiple of the srcWidth * srcHeight";a.Pd(this.Nd);var Q=d.byteLength/(g*l);E=E||a.AlphaType.Unpremul;K=K||a.ColorType.RGBA_8888;O=O||a.ColorSpace.SRGB;var W=Q*g;Q=x(d,"HEAPU8");g=this._writePixels({width:g,
height:l,colorType:K,alphaType:E,colorSpace:O},Q,W,r,y);q(Q,d);return g};a.ColorFilter.MakeBlend=function(d,g){d=A(d);return a.ColorFilter._MakeBlend(d,g)};a.ColorFilter.MakeMatrix=function(d){if(!d||20!==d.length)throw"invalid color matrix";var g=x(d,"HEAPF32"),l=a.ColorFilter._makeMatrix(g);q(g,d);return l};a.ContourMeasure.prototype.getPosTan=function(d,g){this._getPosTan(d,ia);d=Ha.toTypedArray();return g?(g.set(d),g):d.slice()};a.ImageFilter.MakeMatrixTransform=function(d,g,l){d=H(d);if("B"in
g&&"C"in g)return a.ImageFilter._MakeMatrixTransformCubic(d,g.ug,g.vg,l);const r=g.filter;let y=a.MipmapMode.None;"mipmap"in g&&(y=g.mipmap);return a.ImageFilter._MakeMatrixTransformOptions(d,r,y,l)};a.Paint.prototype.getColor=function(){this._getColor(fb);return S(fb)};a.Paint.prototype.setColor=function(d,g){g=g||null;d=A(d);this._setColor(d,g)};a.Paint.prototype.setColorComponents=function(d,g,l,r,y){y=y||null;d=N(d,g,l,r);this._setColor(d,y)};a.Path.prototype.getPoint=function(d,g){this._getPoint(d,
ia);d=Ha.toTypedArray();return g?(g[0]=d[0],g[1]=d[1],g):d.slice(0,2)};a.Picture.prototype.makeShader=function(d,g,l,r,y){r=H(r);y=T(y);return this._makeShader(d,g,l,r,y)};a.PictureRecorder.prototype.beginRecording=function(d){d=T(d);return this._beginRecording(d)};a.Surface.prototype.getCanvas=function(){var d=this._getCanvas();d.Nd=this.Nd;return d};a.Surface.prototype.makeImageSnapshot=function(d){a.Pd(this.Nd);d=x(d,"HEAP32",xb);return this._makeImageSnapshot(d)};a.Surface.prototype.makeSurface=
function(d){a.Pd(this.Nd);d=this._makeSurface(d);d.Nd=this.Nd;return d};a.Surface.prototype.Ef=function(d,g){this.Ie||(this.Ie=this.getCanvas());requestAnimationFrame(function(){a.Pd(this.Nd);d(this.Ie);this.flush(g)}.bind(this))};a.Surface.prototype.requestAnimationFrame||(a.Surface.prototype.requestAnimationFrame=a.Surface.prototype.Ef);a.Surface.prototype.Af=function(d,g){this.Ie||(this.Ie=this.getCanvas());requestAnimationFrame(function(){a.Pd(this.Nd);d(this.Ie);this.flush(g);this.dispose()}.bind(this))};
a.Surface.prototype.drawOnce||(a.Surface.prototype.drawOnce=a.Surface.prototype.Af);a.PathEffect.MakeDash=function(d,g){g||(g=0);if(!d.length||1===d.length%2)throw"Intervals array must have even length";var l=x(d,"HEAPF32");g=a.PathEffect._MakeDash(l,d.length,g);q(l,d);return g};a.PathEffect.MakeLine2D=function(d,g){g=H(g);return a.PathEffect._MakeLine2D(d,g)};a.PathEffect.MakePath2D=function(d,g){d=H(d);return a.PathEffect._MakePath2D(d,g)};a.Shader.MakeColor=function(d,g){g=g||null;d=A(d);return a.Shader._MakeColor(d,
g)};a.Shader.Blend=a.Shader.MakeBlend;a.Shader.Color=a.Shader.MakeColor;a.Shader.MakeLinearGradient=function(d,g,l,r,y,E,K,O){O=O||null;var Q=C(l),W=x(r,"HEAPF32");K=K||0;E=H(E);var u=Ha.toTypedArray();u.set(d);u.set(g,2);d=a.Shader._MakeLinearGradient(ia,Q.de,Q.colorType,W,Q.count,y,K,E,O);q(Q.de,l);r&&q(W,r);return d};a.Shader.MakeRadialGradient=function(d,g,l,r,y,E,K,O){O=O||null;var Q=C(l),W=x(r,"HEAPF32");K=K||0;E=H(E);d=a.Shader._MakeRadialGradient(d[0],d[1],g,Q.de,Q.colorType,W,Q.count,y,K,
E,O);q(Q.de,l);r&&q(W,r);return d};a.Shader.MakeSweepGradient=function(d,g,l,r,y,E,K,O,Q,W){W=W||null;var u=C(l),I=x(r,"HEAPF32");K=K||0;O=O||0;Q=Q||360;E=H(E);d=a.Shader._MakeSweepGradient(d,g,u.de,u.colorType,I,u.count,y,O,Q,K,E,W);q(u.de,l);r&&q(I,r);return d};a.Shader.MakeTwoPointConicalGradient=function(d,g,l,r,y,E,K,O,Q,W){W=W||null;var u=C(y),I=x(E,"HEAPF32");Q=Q||0;O=H(O);var R=Ha.toTypedArray();R.set(d);R.set(l,2);d=a.Shader._MakeTwoPointConicalGradient(ia,g,r,u.de,u.colorType,I,u.count,
K,Q,O,W);q(u.de,y);E&&q(I,E);return d};a.Vertices.prototype.bounds=function(d){this._bounds(ia);var g=Ha.toTypedArray();return d?(d.set(g),d):g.slice()};a.Wd&&a.Wd.forEach(function(d){d()})};a.computeTonalColors=function(e){var d=x(e.ambient,"HEAPF32"),g=x(e.spot,"HEAPF32");this._computeTonalColors(d,g);var l={ambient:S(d),spot:S(g)};q(d,e.ambient);q(g,e.spot);return l};a.LTRBRect=function(e,d,g,l){return Float32Array.of(e,d,g,l)};a.XYWHRect=function(e,d,g,l){return Float32Array.of(e,d,e+g,d+l)};
a.LTRBiRect=function(e,d,g,l){return Int32Array.of(e,d,g,l)};a.XYWHiRect=function(e,d,g,l){return Int32Array.of(e,d,e+g,d+l)};a.RRectXY=function(e,d,g){return Float32Array.of(e[0],e[1],e[2],e[3],d,g,d,g,d,g,d,g)};a.MakeAnimatedImageFromEncoded=function(e){e=new Uint8Array(e);var d=a._malloc(e.byteLength);a.HEAPU8.set(e,d);return(e=a._decodeAnimatedImage(d,e.byteLength))?e:null};a.MakeImageFromEncoded=function(e){e=new Uint8Array(e);var d=a._malloc(e.byteLength);a.HEAPU8.set(e,d);return(e=a._decodeImage(d,
e.byteLength))?e:null};var ib=null;a.MakeImageFromCanvasImageSource=function(e){var d=e.width,g=e.height;ib||(ib=document.createElement("canvas"));ib.width=d;ib.height=g;var l=ib.getContext("2d",{Cg:!0});l.drawImage(e,0,0);e=l.getImageData(0,0,d,g);return a.MakeImage({width:d,height:g,alphaType:a.AlphaType.Unpremul,colorType:a.ColorType.RGBA_8888,colorSpace:a.ColorSpace.SRGB},e.data,4*d)};a.MakeImage=function(e,d,g){var l=a._malloc(d.length);a.HEAPU8.set(d,l);return a._MakeImage(e,l,d.length,g)};
a.MakeVertices=function(e,d,g,l,r,y){var E=r&&r.length||0,K=0;g&&g.length&&(K|=1);l&&l.length&&(K|=2);void 0===y||y||(K|=4);e=new a._VerticesBuilder(e,d.length/2,E,K);x(d,"HEAPF32",e.positions());e.texCoords()&&x(g,"HEAPF32",e.texCoords());e.colors()&&x(m(l),"HEAPU32",e.colors());e.indices()&&x(r,"HEAPU16",e.indices());return e.detach()};a.Matrix={};a.Matrix.identity=function(){return c(3)};a.Matrix.invert=function(e){var d=e[0]*e[4]*e[8]+e[1]*e[5]*e[6]+e[2]*e[3]*e[7]-e[2]*e[4]*e[6]-e[1]*e[3]*e[8]-
e[0]*e[5]*e[7];return d?[(e[4]*e[8]-e[5]*e[7])/d,(e[2]*e[7]-e[1]*e[8])/d,(e[1]*e[5]-e[2]*e[4])/d,(e[5]*e[6]-e[3]*e[8])/d,(e[0]*e[8]-e[2]*e[6])/d,(e[2]*e[3]-e[0]*e[5])/d,(e[3]*e[7]-e[4]*e[6])/d,(e[1]*e[6]-e[0]*e[7])/d,(e[0]*e[4]-e[1]*e[3])/d]:null};a.Matrix.mapPoints=function(e,d){for(var g=0;g<d.length;g+=2){var l=d[g],r=d[g+1],y=e[6]*l+e[7]*r+e[8],E=e[3]*l+e[4]*r+e[5];d[g]=(e[0]*l+e[1]*r+e[2])/y;d[g+1]=E/y}return d};a.Matrix.multiply=function(){return hb(3,arguments)};a.Matrix.rotated=function(e,
d,g){d=d||0;g=g||0;var l=Math.sin(e);e=Math.cos(e);return[e,-l,ta(l,g,1-e,d),l,e,ta(-l,d,1-e,g),0,0,1]};a.Matrix.scaled=function(e,d,g,l){g=g||0;l=l||0;var r=b([e,d],c(3),3,0,1);return b([g-e*g,l-d*l],r,3,2,0)};a.Matrix.skewed=function(e,d,g,l){g=g||0;l=l||0;var r=b([e,d],c(3),3,1,-1);return b([-e*g,-d*l],r,3,2,0)};a.Matrix.translated=function(e,d){return b(arguments,c(3),3,2,0)};a.Vector={};a.Vector.dot=function(e,d){return e.map(function(g,l){return g*d[l]}).reduce(function(g,l){return g+l})};a.Vector.lengthSquared=
function(e){return a.Vector.dot(e,e)};a.Vector.length=function(e){return Math.sqrt(a.Vector.lengthSquared(e))};a.Vector.mulScalar=function(e,d){return e.map(function(g){return g*d})};a.Vector.add=function(e,d){return e.map(function(g,l){return g+d[l]})};a.Vector.sub=function(e,d){return e.map(function(g,l){return g-d[l]})};a.Vector.dist=function(e,d){return a.Vector.length(a.Vector.sub(e,d))};a.Vector.normalize=function(e){return a.Vector.mulScalar(e,1/a.Vector.length(e))};a.Vector.cross=function(e,
d){return[e[1]*d[2]-e[2]*d[1],e[2]*d[0]-e[0]*d[2],e[0]*d[1]-e[1]*d[0]]};a.M44={};a.M44.identity=function(){return c(4)};a.M44.translated=function(e){return b(e,c(4),4,3,0)};a.M44.scaled=function(e){return b(e,c(4),4,0,1)};a.M44.rotated=function(e,d){return a.M44.rotatedUnitSinCos(a.Vector.normalize(e),Math.sin(d),Math.cos(d))};a.M44.rotatedUnitSinCos=function(e,d,g){var l=e[0],r=e[1];e=e[2];var y=1-g;return[y*l*l+g,y*l*r-d*e,y*l*e+d*r,0,y*l*r+d*e,y*r*r+g,y*r*e-d*l,0,y*l*e-d*r,y*r*e+d*l,y*e*e+g,0,
0,0,0,1]};a.M44.lookat=function(e,d,g){d=a.Vector.normalize(a.Vector.sub(d,e));g=a.Vector.normalize(g);g=a.Vector.normalize(a.Vector.cross(d,g));var l=a.M44.identity();b(g,l,4,0,0);b(a.Vector.cross(g,d),l,4,1,0);b(a.Vector.mulScalar(d,-1),l,4,2,0);b(e,l,4,3,0);e=a.M44.invert(l);return null===e?a.M44.identity():e};a.M44.perspective=function(e,d,g){var l=1/(d-e);g/=2;g=Math.cos(g)/Math.sin(g);return[g,0,0,0,0,g,0,0,0,0,(d+e)*l,2*d*e*l,0,0,-1,1]};a.M44.rc=function(e,d,g){return e[4*d+g]};a.M44.multiply=
function(){return hb(4,arguments)};a.M44.invert=function(e){var d=e[0],g=e[4],l=e[8],r=e[12],y=e[1],E=e[5],K=e[9],O=e[13],Q=e[2],W=e[6],u=e[10],I=e[14],R=e[3],aa=e[7],ja=e[11];e=e[15];var qa=d*E-g*y,ua=d*K-l*y,Aa=d*O-r*y,ea=g*K-l*E,G=g*O-r*E,k=l*O-r*K,p=Q*aa-W*R,z=Q*ja-u*R,B=Q*e-I*R,D=W*ja-u*aa,F=W*e-I*aa,L=u*e-I*ja,ba=qa*L-ua*F+Aa*D+ea*B-G*z+k*p,ca=1/ba;if(0===ba||Infinity===ca)return null;qa*=ca;ua*=ca;Aa*=ca;ea*=ca;G*=ca;k*=ca;p*=ca;z*=ca;B*=ca;D*=ca;F*=ca;L*=ca;d=[E*L-K*F+O*D,K*B-y*L-O*z,y*F-
E*B+O*p,E*z-y*D-K*p,l*F-g*L-r*D,d*L-l*B+r*z,g*B-d*F-r*p,d*D-g*z+l*p,aa*k-ja*G+e*ea,ja*Aa-R*k-e*ua,R*G-aa*Aa+e*qa,aa*ua-R*ea-ja*qa,u*G-W*k-I*ea,Q*k-u*Aa+I*ua,W*Aa-Q*G-I*qa,Q*ea-W*ua+u*qa];return d.every(function(Ia){return!isNaN(Ia)&&Infinity!==Ia&&-Infinity!==Ia})?d:null};a.M44.transpose=function(e){return[e[0],e[4],e[8],e[12],e[1],e[5],e[9],e[13],e[2],e[6],e[10],e[14],e[3],e[7],e[11],e[15]]};a.M44.mustInvert=function(e){e=a.M44.invert(e);if(null===e)throw"Matrix not invertible";return e};a.M44.setupCamera=
function(e,d,g){var l=a.M44.lookat(g.eye,g.coa,g.up);g=a.M44.perspective(g.near,g.far,g.angle);d=[(e[2]-e[0])/2,(e[3]-e[1])/2,d];e=a.M44.multiply(a.M44.translated([(e[0]+e[2])/2,(e[1]+e[3])/2,0]),a.M44.scaled(d));return a.M44.multiply(e,g,l,a.M44.mustInvert(e))};a.ColorMatrix={};a.ColorMatrix.identity=function(){var e=new Float32Array(20);e[0]=1;e[6]=1;e[12]=1;e[18]=1;return e};a.ColorMatrix.scaled=function(e,d,g,l){var r=new Float32Array(20);r[0]=e;r[6]=d;r[12]=g;r[18]=l;return r};var Ad=[[6,7,11,
12],[0,10,2,12],[0,1,5,6]];a.ColorMatrix.rotated=function(e,d,g){var l=a.ColorMatrix.identity();e=Ad[e];l[e[0]]=g;l[e[1]]=d;l[e[2]]=-d;l[e[3]]=g;return l};a.ColorMatrix.postTranslate=function(e,d,g,l,r){e[4]+=d;e[9]+=g;e[14]+=l;e[19]+=r;return e};a.ColorMatrix.concat=function(e,d){for(var g=new Float32Array(20),l=0,r=0;20>r;r+=5){for(var y=0;4>y;y++)g[l++]=e[r]*d[y]+e[r+1]*d[y+5]+e[r+2]*d[y+10]+e[r+3]*d[y+15];g[l++]=e[r]*d[4]+e[r+1]*d[9]+e[r+2]*d[14]+e[r+3]*d[19]+e[r+4]}return g};(function(e){e.Wd=
e.Wd||[];e.Wd.push(function(){function d(u){if(!u||!u.length)return[];for(var I=[],R=0;R<u.length;R+=5){var aa=e.LTRBRect(u[R],u[R+1],u[R+2],u[R+3]);aa.direction=0===u[R+4]?e.TextDirection.RTL:e.TextDirection.LTR;I.push(aa)}e._free(u.byteOffset);return I}function g(u){u=u||{};void 0===u.weight&&(u.weight=e.FontWeight.Normal);u.width=u.width||e.FontWidth.Normal;u.slant=u.slant||e.FontSlant.Upright;return u}function l(u){if(!u||!u.length)return V;for(var I=[],R=0;R<u.length;R++){var aa=r(u[R]);I.push(aa)}return x(I,
"HEAPU32")}function r(u){if(K[u])return K[u];var I=oa(u)+1,R=e._malloc(I);ra(u,J,R,I);return K[u]=R}function y(u){u._colorPtr=A(u.color);u._foregroundColorPtr=V;u._backgroundColorPtr=V;u._decorationColorPtr=V;u.foregroundColor&&(u._foregroundColorPtr=A(u.foregroundColor,O));u.backgroundColor&&(u._backgroundColorPtr=A(u.backgroundColor,Q));u.decorationColor&&(u._decorationColorPtr=A(u.decorationColor,W));Array.isArray(u.fontFamilies)&&u.fontFamilies.length?(u._fontFamiliesPtr=l(u.fontFamilies),u._fontFamiliesLen=
u.fontFamilies.length):(u._fontFamiliesPtr=V,u._fontFamiliesLen=0);if(u.locale){var I=u.locale;u._localePtr=r(I);u._localeLen=oa(I)+1}else u._localePtr=V,u._localeLen=0;if(Array.isArray(u.shadows)&&u.shadows.length){I=u.shadows;var R=I.map(function(ea){return ea.color||e.BLACK}),aa=I.map(function(ea){return ea.blurRadius||0});u._shadowLen=I.length;for(var ja=e._malloc(8*I.length),qa=ja/4,ua=0;ua<I.length;ua++){var Aa=I[ua].offset||[0,0];e.HEAPF32[qa]=Aa[0];e.HEAPF32[qa+1]=Aa[1];qa+=2}u._shadowColorsPtr=
C(R).de;u._shadowOffsetsPtr=ja;u._shadowBlurRadiiPtr=x(aa,"HEAPF32")}else u._shadowLen=0,u._shadowColorsPtr=V,u._shadowOffsetsPtr=V,u._shadowBlurRadiiPtr=V;Array.isArray(u.fontFeatures)&&u.fontFeatures.length?(I=u.fontFeatures,R=I.map(function(ea){return ea.name}),aa=I.map(function(ea){return ea.value}),u._fontFeatureLen=I.length,u._fontFeatureNamesPtr=l(R),u._fontFeatureValuesPtr=x(aa,"HEAPU32")):(u._fontFeatureLen=0,u._fontFeatureNamesPtr=V,u._fontFeatureValuesPtr=V)}function E(u){e._free(u._fontFamiliesPtr);
e._free(u._shadowColorsPtr);e._free(u._shadowOffsetsPtr);e._free(u._shadowBlurRadiiPtr);e._free(u._fontFeatureNamesPtr);e._free(u._fontFeatureValuesPtr)}e.Paragraph.prototype.getRectsForRange=function(u,I,R,aa){u=this._getRectsForRange(u,I,R,aa);return d(u)};e.Paragraph.prototype.getRectsForPlaceholders=function(){var u=this._getRectsForPlaceholders();return d(u)};e.TypefaceFontProvider.prototype.registerFont=function(u,I){u=e.Typeface.MakeFreeTypeFaceFromData(u);if(!u)return null;I=r(I);this._registerFont(u,
I)};e.ParagraphStyle=function(u){u.disableHinting=u.disableHinting||!1;if(u.ellipsis){var I=u.ellipsis;u._ellipsisPtr=r(I);u._ellipsisLen=oa(I)+1}else u._ellipsisPtr=V,u._ellipsisLen=0;u.heightMultiplier=u.heightMultiplier||0;u.maxLines=u.maxLines||0;I=(I=u.strutStyle)||{};I.strutEnabled=I.strutEnabled||!1;I.strutEnabled&&Array.isArray(I.fontFamilies)&&I.fontFamilies.length?(I._fontFamiliesPtr=l(I.fontFamilies),I._fontFamiliesLen=I.fontFamilies.length):(I._fontFamiliesPtr=V,I._fontFamiliesLen=0);
I.fontStyle=g(I.fontStyle);I.fontSize=I.fontSize||0;I.heightMultiplier=I.heightMultiplier||0;I.halfLeading=I.halfLeading||!1;I.leading=I.leading||0;I.forceStrutHeight=I.forceStrutHeight||!1;u.strutStyle=I;u.textAlign=u.textAlign||e.TextAlign.Start;u.textDirection=u.textDirection||e.TextDirection.LTR;u.textHeightBehavior=u.textHeightBehavior||e.TextHeightBehavior.All;u.textStyle=e.TextStyle(u.textStyle);return u};e.TextStyle=function(u){u.color||(u.color=e.BLACK);u.decoration=u.decoration||0;u.decorationThickness=
u.decorationThickness||0;u.decorationStyle=u.decorationStyle||e.DecorationStyle.Solid;u.textBaseline=u.textBaseline||e.TextBaseline.Alphabetic;u.fontSize=u.fontSize||0;u.letterSpacing=u.letterSpacing||0;u.wordSpacing=u.wordSpacing||0;u.heightMultiplier=u.heightMultiplier||0;u.halfLeading=u.halfLeading||!1;u.fontStyle=g(u.fontStyle);return u};var K={},O=e._malloc(16),Q=e._malloc(16),W=e._malloc(16);e.ParagraphBuilder.Make=function(u,I){y(u.textStyle);I=e.ParagraphBuilder._Make(u,I);E(u.textStyle);
return I};e.ParagraphBuilder.MakeFromFontProvider=function(u,I){y(u.textStyle);I=e.ParagraphBuilder._MakeFromFontProvider(u,I);E(u.textStyle);return I};e.ParagraphBuilder.ShapeText=function(u,I,R){let aa=0;for(const ja of I)aa+=ja.length;if(aa!==u.length)throw"Accumulated block lengths must equal text.length";return e.ParagraphBuilder._ShapeText(u,I,R)};e.ParagraphBuilder.prototype.pushStyle=function(u){y(u);this._pushStyle(u);E(u)};e.ParagraphBuilder.prototype.pushPaintStyle=function(u,I,R){y(u);
this._pushPaintStyle(u,I,R);E(u)};e.ParagraphBuilder.prototype.addPlaceholder=function(u,I,R,aa,ja){R=R||e.PlaceholderAlignment.Baseline;aa=aa||e.TextBaseline.Alphabetic;this._addPlaceholder(u||0,I||0,R,aa,ja||0)}})})(v);a.Wd=a.Wd||[];a.Wd.push(function(){a.Path.prototype.op=function(e,d){return this._op(e,d)?this:null};a.Path.prototype.simplify=function(){return this._simplify()?this:null}});a.Wd=a.Wd||[];a.Wd.push(function(){a.Canvas.prototype.drawText=function(e,d,g,l,r){var y=oa(e),E=a._malloc(y+
1);ra(e,J,E,y+1);this._drawSimpleText(E,y,d,g,r,l);a._free(E)};a.Canvas.prototype.drawGlyphs=function(e,d,g,l,r,y){if(!(2*e.length<=d.length))throw"Not enough positions for the array of gyphs";a.Pd(this.Nd);const E=x(e,"HEAPU16"),K=x(d,"HEAPF32");this._drawGlyphs(e.length,E,K,g,l,r,y);q(K,d);q(E,e)};a.Font.prototype.getGlyphBounds=function(e,d,g){var l=x(e,"HEAPU16"),r=a._malloc(16*e.length);this._getGlyphWidthBounds(l,e.length,V,r,d||null);d=new Float32Array(a.HEAPU8.buffer,r,4*e.length);q(l,e);
if(g)return g.set(d),a._free(r),g;e=Float32Array.from(d);a._free(r);return e};a.Font.prototype.getGlyphIDs=function(e,d,g){d||(d=e.length);var l=oa(e)+1,r=a._malloc(l);ra(e,J,r,l);e=a._malloc(2*d);d=this._getGlyphIDs(r,l-1,d,e);a._free(r);if(0>d)return a._free(e),null;r=new Uint16Array(a.HEAPU8.buffer,e,d);if(g)return g.set(r),a._free(e),g;g=Uint16Array.from(r);a._free(e);return g};a.Font.prototype.getGlyphIntercepts=function(e,d,g,l){var r=x(e,"HEAPU16"),y=x(d,"HEAPF32");return this._getGlyphIntercepts(r,
e.length,!(e&&e._ck),y,d.length,!(d&&d._ck),g,l)};a.Font.prototype.getGlyphWidths=function(e,d,g){var l=x(e,"HEAPU16"),r=a._malloc(4*e.length);this._getGlyphWidthBounds(l,e.length,r,V,d||null);d=new Float32Array(a.HEAPU8.buffer,r,e.length);q(l,e);if(g)return g.set(d),a._free(r),g;e=Float32Array.from(d);a._free(r);return e};a.FontMgr.FromData=function(){if(!arguments.length)return null;var e=arguments;1===e.length&&Array.isArray(e[0])&&(e=arguments[0]);if(!e.length)return null;for(var d=[],g=[],l=
0;l<e.length;l++){var r=new Uint8Array(e[l]),y=x(r,"HEAPU8");d.push(y);g.push(r.byteLength)}d=x(d,"HEAPU32");g=x(g,"HEAPU32");e=a.FontMgr._fromData(d,g,e.length);a._free(d);a._free(g);return e};a.Typeface.MakeFreeTypeFaceFromData=function(e){e=new Uint8Array(e);var d=x(e,"HEAPU8");return(e=a.Typeface._MakeFreeTypeFaceFromData(d,e.byteLength))?e:null};a.Typeface.prototype.getGlyphIDs=function(e,d,g){d||(d=e.length);var l=oa(e)+1,r=a._malloc(l);ra(e,J,r,l);e=a._malloc(2*d);d=this._getGlyphIDs(r,l-1,
d,e);a._free(r);if(0>d)return a._free(e),null;r=new Uint16Array(a.HEAPU8.buffer,e,d);if(g)return g.set(r),a._free(e),g;g=Uint16Array.from(r);a._free(e);return g};a.TextBlob.MakeOnPath=function(e,d,g,l){if(e&&e.length&&d&&d.countPoints()){if(1===d.countPoints())return this.MakeFromText(e,g);l||(l=0);var r=g.getGlyphIDs(e);r=g.getGlyphWidths(r);var y=[];d=new a.ContourMeasureIter(d,!1,1);for(var E=d.next(),K=new Float32Array(4),O=0;O<e.length&&E;O++){var Q=r[O];l+=Q/2;if(l>E.length()){E.delete();E=
d.next();if(!E){e=e.substring(0,O);break}l=Q/2}E.getPosTan(l,K);var W=K[2],u=K[3];y.push(W,u,K[0]-Q/2*W,K[1]-Q/2*u);l+=Q/2}e=this.MakeFromRSXform(e,y,g);E&&E.delete();d.delete();return e}};a.TextBlob.MakeFromRSXform=function(e,d,g){var l=oa(e)+1,r=a._malloc(l);ra(e,J,r,l);e=x(d,"HEAPF32");g=a.TextBlob._MakeFromRSXform(r,l-1,e,g);a._free(r);return g?g:null};a.TextBlob.MakeFromRSXformGlyphs=function(e,d,g){var l=x(e,"HEAPU16");d=x(d,"HEAPF32");g=a.TextBlob._MakeFromRSXformGlyphs(l,2*e.length,d,g);q(l,
e);return g?g:null};a.TextBlob.MakeFromGlyphs=function(e,d){var g=x(e,"HEAPU16");d=a.TextBlob._MakeFromGlyphs(g,2*e.length,d);q(g,e);return d?d:null};a.TextBlob.MakeFromText=function(e,d){var g=oa(e)+1,l=a._malloc(g);ra(e,J,l,g);e=a.TextBlob._MakeFromText(l,g-1,d);a._free(l);return e?e:null};a.MallocGlyphIDs=function(e){return a.Malloc(Uint16Array,e)}});a.Wd=a.Wd||[];a.Wd.push(function(){a.MakePicture=function(e){e=new Uint8Array(e);var d=a._malloc(e.byteLength);a.HEAPU8.set(e,d);return(e=a._MakePicture(d,
e.byteLength))?e:null}});(function(){function e(G){for(var k=0;k<G.length;k++)if(void 0!==G[k]&&!Number.isFinite(G[k]))return!1;return!0}function d(G){var k=a.getColorComponents(G);G=k[0];var p=k[1],z=k[2];k=k[3];if(1===k)return G=G.toString(16).toLowerCase(),p=p.toString(16).toLowerCase(),z=z.toString(16).toLowerCase(),G=1===G.length?"0"+G:G,p=1===p.length?"0"+p:p,z=1===z.length?"0"+z:z,"#"+G+p+z;k=0===k||1===k?k:k.toFixed(8);return"rgba("+G+", "+p+", "+z+", "+k+")"}function g(G){return a.parseColorString(G,
ua)}function l(G){G=Aa.exec(G);if(!G)return null;var k=parseFloat(G[4]),p=16;switch(G[5]){case "em":case "rem":p=16*k;break;case "pt":p=4*k/3;break;case "px":p=k;break;case "pc":p=16*k;break;case "in":p=96*k;break;case "cm":p=96*k/2.54;break;case "mm":p=96/25.4*k;break;case "q":p=96/25.4/4*k;break;case "%":p=16/75*k}return{style:G[1],variant:G[2],weight:G[3],sizePx:p,family:G[6].trim()}}function r(G){this.Od=G;this.Rd=new a.Paint;this.Rd.setAntiAlias(!0);this.Rd.setStrokeMiter(10);this.Rd.setStrokeCap(a.StrokeCap.Butt);
this.Rd.setStrokeJoin(a.StrokeJoin.Miter);this.Re="10px monospace";this.oe=new a.Font(null,10);this.oe.setSubpixel(!0);this.ce=this.he=a.BLACK;this.xe=0;this.Ke=a.TRANSPARENT;this.ze=this.ye=0;this.Le=this.le=1;this.Je=0;this.we=[];this.Qd=a.BlendMode.SrcOver;this.Rd.setStrokeWidth(this.Le);this.Rd.setBlendMode(this.Qd);this.Td=new a.Path;this.Vd=a.Matrix.identity();this.mf=[];this.De=[];this.ne=function(){this.Td.delete();this.Rd.delete();this.oe.delete();this.De.forEach(function(k){k.ne()})};Object.defineProperty(this,
"currentTransform",{enumerable:!0,get:function(){return{a:this.Vd[0],c:this.Vd[1],e:this.Vd[2],b:this.Vd[3],d:this.Vd[4],f:this.Vd[5]}},set:function(k){k.a&&this.setTransform(k.a,k.b,k.c,k.d,k.e,k.f)}});Object.defineProperty(this,"fillStyle",{enumerable:!0,get:function(){return f(this.ce)?d(this.ce):this.ce},set:function(k){"string"===typeof k?this.ce=g(k):k.ve&&(this.ce=k)}});Object.defineProperty(this,"font",{enumerable:!0,get:function(){return this.Re},set:function(k){var p=l(k),z=p.family;p.typeface=
ea[z]?ea[z][(p.style||"normal")+"|"+(p.variant||"normal")+"|"+(p.weight||"normal")]||ea[z]["*"]:null;p&&(this.oe.setSize(p.sizePx),this.oe.setTypeface(p.typeface),this.Re=k)}});Object.defineProperty(this,"globalAlpha",{enumerable:!0,get:function(){return this.le},set:function(k){!isFinite(k)||0>k||1<k||(this.le=k)}});Object.defineProperty(this,"globalCompositeOperation",{enumerable:!0,get:function(){switch(this.Qd){case a.BlendMode.SrcOver:return"source-over";case a.BlendMode.DstOver:return"destination-over";
case a.BlendMode.Src:return"copy";case a.BlendMode.Dst:return"destination";case a.BlendMode.Clear:return"clear";case a.BlendMode.SrcIn:return"source-in";case a.BlendMode.DstIn:return"destination-in";case a.BlendMode.SrcOut:return"source-out";case a.BlendMode.DstOut:return"destination-out";case a.BlendMode.SrcATop:return"source-atop";case a.BlendMode.DstATop:return"destination-atop";case a.BlendMode.Xor:return"xor";case a.BlendMode.Plus:return"lighter";case a.BlendMode.Multiply:return"multiply";case a.BlendMode.Screen:return"screen";
case a.BlendMode.Overlay:return"overlay";case a.BlendMode.Darken:return"darken";case a.BlendMode.Lighten:return"lighten";case a.BlendMode.ColorDodge:return"color-dodge";case a.BlendMode.ColorBurn:return"color-burn";case a.BlendMode.HardLight:return"hard-light";case a.BlendMode.SoftLight:return"soft-light";case a.BlendMode.Difference:return"difference";case a.BlendMode.Exclusion:return"exclusion";case a.BlendMode.Hue:return"hue";case a.BlendMode.Saturation:return"saturation";case a.BlendMode.Color:return"color";
case a.BlendMode.Luminosity:return"luminosity"}},set:function(k){switch(k){case "source-over":this.Qd=a.BlendMode.SrcOver;break;case "destination-over":this.Qd=a.BlendMode.DstOver;break;case "copy":this.Qd=a.BlendMode.Src;break;case "destination":this.Qd=a.BlendMode.Dst;break;case "clear":this.Qd=a.BlendMode.Clear;break;case "source-in":this.Qd=a.BlendMode.SrcIn;break;case "destination-in":this.Qd=a.BlendMode.DstIn;break;case "source-out":this.Qd=a.BlendMode.SrcOut;break;case "destination-out":this.Qd=
a.BlendMode.DstOut;break;case "source-atop":this.Qd=a.BlendMode.SrcATop;break;case "destination-atop":this.Qd=a.BlendMode.DstATop;break;case "xor":this.Qd=a.BlendMode.Xor;break;case "lighter":this.Qd=a.BlendMode.Plus;break;case "plus-lighter":this.Qd=a.BlendMode.Plus;break;case "plus-darker":throw"plus-darker is not supported";case "multiply":this.Qd=a.BlendMode.Multiply;break;case "screen":this.Qd=a.BlendMode.Screen;break;case "overlay":this.Qd=a.BlendMode.Overlay;break;case "darken":this.Qd=a.BlendMode.Darken;
break;case "lighten":this.Qd=a.BlendMode.Lighten;break;case "color-dodge":this.Qd=a.BlendMode.ColorDodge;break;case "color-burn":this.Qd=a.BlendMode.ColorBurn;break;case "hard-light":this.Qd=a.BlendMode.HardLight;break;case "soft-light":this.Qd=a.BlendMode.SoftLight;break;case "difference":this.Qd=a.BlendMode.Difference;break;case "exclusion":this.Qd=a.BlendMode.Exclusion;break;case "hue":this.Qd=a.BlendMode.Hue;break;case "saturation":this.Qd=a.BlendMode.Saturation;break;case "color":this.Qd=a.BlendMode.Color;
break;case "luminosity":this.Qd=a.BlendMode.Luminosity;break;default:return}this.Rd.setBlendMode(this.Qd)}});Object.defineProperty(this,"imageSmoothingEnabled",{enumerable:!0,get:function(){return!0},set:function(){}});Object.defineProperty(this,"imageSmoothingQuality",{enumerable:!0,get:function(){return"high"},set:function(){}});Object.defineProperty(this,"lineCap",{enumerable:!0,get:function(){switch(this.Rd.getStrokeCap()){case a.StrokeCap.Butt:return"butt";case a.StrokeCap.Round:return"round";
case a.StrokeCap.Square:return"square"}},set:function(k){switch(k){case "butt":this.Rd.setStrokeCap(a.StrokeCap.Butt);break;case "round":this.Rd.setStrokeCap(a.StrokeCap.Round);break;case "square":this.Rd.setStrokeCap(a.StrokeCap.Square)}}});Object.defineProperty(this,"lineDashOffset",{enumerable:!0,get:function(){return this.Je},set:function(k){isFinite(k)&&(this.Je=k)}});Object.defineProperty(this,"lineJoin",{enumerable:!0,get:function(){switch(this.Rd.getStrokeJoin()){case a.StrokeJoin.Miter:return"miter";
case a.StrokeJoin.Round:return"round";case a.StrokeJoin.Bevel:return"bevel"}},set:function(k){switch(k){case "miter":this.Rd.setStrokeJoin(a.StrokeJoin.Miter);break;case "round":this.Rd.setStrokeJoin(a.StrokeJoin.Round);break;case "bevel":this.Rd.setStrokeJoin(a.StrokeJoin.Bevel)}}});Object.defineProperty(this,"lineWidth",{enumerable:!0,get:function(){return this.Rd.getStrokeWidth()},set:function(k){0>=k||!k||(this.Le=k,this.Rd.setStrokeWidth(k))}});Object.defineProperty(this,"miterLimit",{enumerable:!0,
get:function(){return this.Rd.getStrokeMiter()},set:function(k){0>=k||!k||this.Rd.setStrokeMiter(k)}});Object.defineProperty(this,"shadowBlur",{enumerable:!0,get:function(){return this.xe},set:function(k){0>k||!isFinite(k)||(this.xe=k)}});Object.defineProperty(this,"shadowColor",{enumerable:!0,get:function(){return d(this.Ke)},set:function(k){this.Ke=g(k)}});Object.defineProperty(this,"shadowOffsetX",{enumerable:!0,get:function(){return this.ye},set:function(k){isFinite(k)&&(this.ye=k)}});Object.defineProperty(this,
"shadowOffsetY",{enumerable:!0,get:function(){return this.ze},set:function(k){isFinite(k)&&(this.ze=k)}});Object.defineProperty(this,"strokeStyle",{enumerable:!0,get:function(){return d(this.he)},set:function(k){"string"===typeof k?this.he=g(k):k.ve&&(this.he=k)}});this.arc=function(k,p,z,B,D,F){I(this.Td,k,p,z,z,0,B,D,F)};this.arcTo=function(k,p,z,B,D){Q(this.Td,k,p,z,B,D)};this.beginPath=function(){this.Td.delete();this.Td=new a.Path};this.bezierCurveTo=function(k,p,z,B,D,F){var L=this.Td;e([k,
p,z,B,D,F])&&(L.isEmpty()&&L.moveTo(k,p),L.cubicTo(k,p,z,B,D,F))};this.clearRect=function(k,p,z,B){this.Rd.setStyle(a.PaintStyle.Fill);this.Rd.setBlendMode(a.BlendMode.Clear);this.Od.drawRect(a.XYWHRect(k,p,z,B),this.Rd);this.Rd.setBlendMode(this.Qd)};this.clip=function(k,p){"string"===typeof k?(p=k,k=this.Td):k&&k.$e&&(k=k.Xd);k||(k=this.Td);k=k.copy();p&&"evenodd"===p.toLowerCase()?k.setFillType(a.FillType.EvenOdd):k.setFillType(a.FillType.Winding);this.Od.clipPath(k,a.ClipOp.Intersect,!0);k.delete()};
this.closePath=function(){W(this.Td)};this.createImageData=function(){if(1===arguments.length){var k=arguments[0];return new K(new Uint8ClampedArray(4*k.width*k.height),k.width,k.height)}if(2===arguments.length){k=arguments[0];var p=arguments[1];return new K(new Uint8ClampedArray(4*k*p),k,p)}throw"createImageData expects 1 or 2 arguments, got "+arguments.length;};this.createLinearGradient=function(k,p,z,B){if(e(arguments)){var D=new O(k,p,z,B);this.De.push(D);return D}};this.createPattern=function(k,
p){k=new ja(k,p);this.De.push(k);return k};this.createRadialGradient=function(k,p,z,B,D,F){if(e(arguments)){var L=new qa(k,p,z,B,D,F);this.De.push(L);return L}};this.drawImage=function(k){k instanceof E&&(k=k.tf());var p=this.Qe();if(3===arguments.length||5===arguments.length)var z=a.XYWHRect(arguments[1],arguments[2],arguments[3]||k.width(),arguments[4]||k.height()),B=a.XYWHRect(0,0,k.width(),k.height());else if(9===arguments.length)z=a.XYWHRect(arguments[5],arguments[6],arguments[7],arguments[8]),
B=a.XYWHRect(arguments[1],arguments[2],arguments[3],arguments[4]);else throw"invalid number of args for drawImage, need 3, 5, or 9; got "+arguments.length;this.Od.drawImageRect(k,B,z,p,!1);p.dispose()};this.ellipse=function(k,p,z,B,D,F,L,ba){I(this.Td,k,p,z,B,D,F,L,ba)};this.Qe=function(){var k=this.Rd.copy();k.setStyle(a.PaintStyle.Fill);if(f(this.ce)){var p=a.multiplyByAlpha(this.ce,this.le);k.setColor(p)}else p=this.ce.ve(this.Vd),k.setColor(a.Color(0,0,0,this.le)),k.setShader(p);k.dispose=function(){this.delete()};
return k};this.fill=function(k,p){"string"===typeof k?(p=k,k=this.Td):k&&k.$e&&(k=k.Xd);if("evenodd"===p)this.Td.setFillType(a.FillType.EvenOdd);else{if("nonzero"!==p&&p)throw"invalid fill rule";this.Td.setFillType(a.FillType.Winding)}k||(k=this.Td);p=this.Qe();var z=this.Ae(p);z&&(this.Od.save(),this.te(),this.Od.drawPath(k,z),this.Od.restore(),z.dispose());this.Od.drawPath(k,p);p.dispose()};this.fillRect=function(k,p,z,B){var D=this.Qe(),F=this.Ae(D);F&&(this.Od.save(),this.te(),this.Od.drawRect(a.XYWHRect(k,
p,z,B),F),this.Od.restore(),F.dispose());this.Od.drawRect(a.XYWHRect(k,p,z,B),D);D.dispose()};this.fillText=function(k,p,z){var B=this.Qe();k=a.TextBlob.MakeFromText(k,this.oe);var D=this.Ae(B);D&&(this.Od.save(),this.te(),this.Od.drawTextBlob(k,p,z,D),this.Od.restore(),D.dispose());this.Od.drawTextBlob(k,p,z,B);k.delete();B.dispose()};this.getImageData=function(k,p,z,B){return(k=this.Od.readPixels(k,p,{width:z,height:B,colorType:a.ColorType.RGBA_8888,alphaType:a.AlphaType.Unpremul,colorSpace:a.ColorSpace.SRGB}))?
new K(new Uint8ClampedArray(k.buffer),z,B):null};this.getLineDash=function(){return this.we.slice()};this.nf=function(k){var p=a.Matrix.invert(this.Vd);a.Matrix.mapPoints(p,k);return k};this.isPointInPath=function(k,p,z){var B=arguments;if(3===B.length)var D=this.Td;else if(4===B.length)D=B[0],k=B[1],p=B[2],z=B[3];else throw"invalid arg count, need 3 or 4, got "+B.length;if(!isFinite(k)||!isFinite(p))return!1;z=z||"nonzero";if("nonzero"!==z&&"evenodd"!==z)return!1;B=this.nf([k,p]);k=B[0];p=B[1];D.setFillType("nonzero"===
z?a.FillType.Winding:a.FillType.EvenOdd);return D.contains(k,p)};this.isPointInStroke=function(k,p){var z=arguments;if(2===z.length)var B=this.Td;else if(3===z.length)B=z[0],k=z[1],p=z[2];else throw"invalid arg count, need 2 or 3, got "+z.length;if(!isFinite(k)||!isFinite(p))return!1;z=this.nf([k,p]);k=z[0];p=z[1];B=B.copy();B.setFillType(a.FillType.Winding);B.stroke({width:this.lineWidth,miter_limit:this.miterLimit,cap:this.Rd.getStrokeCap(),join:this.Rd.getStrokeJoin(),precision:.3});z=B.contains(k,
p);B.delete();return z};this.lineTo=function(k,p){R(this.Td,k,p)};this.measureText=function(k){k=this.oe.getGlyphIDs(k);k=this.oe.getGlyphWidths(k);let p=0;for(const z of k)p+=z;return{width:p}};this.moveTo=function(k,p){var z=this.Td;e([k,p])&&z.moveTo(k,p)};this.putImageData=function(k,p,z,B,D,F,L){if(e([p,z,B,D,F,L]))if(void 0===B)this.Od.writePixels(k.data,k.width,k.height,p,z);else if(B=B||0,D=D||0,F=F||k.width,L=L||k.height,0>F&&(B+=F,F=Math.abs(F)),0>L&&(D+=L,L=Math.abs(L)),0>B&&(F+=B,B=0),
0>D&&(L+=D,D=0),!(0>=F||0>=L)){k=a.MakeImage({width:k.width,height:k.height,alphaType:a.AlphaType.Unpremul,colorType:a.ColorType.RGBA_8888,colorSpace:a.ColorSpace.SRGB},k.data,4*k.width);var ba=a.XYWHRect(B,D,F,L);p=a.XYWHRect(p+B,z+D,F,L);z=a.Matrix.invert(this.Vd);this.Od.save();this.Od.concat(z);this.Od.drawImageRect(k,ba,p,null,!1);this.Od.restore();k.delete()}};this.quadraticCurveTo=function(k,p,z,B){var D=this.Td;e([k,p,z,B])&&(D.isEmpty()&&D.moveTo(k,p),D.quadTo(k,p,z,B))};this.rect=function(k,
p,z,B){var D=this.Td;k=a.XYWHRect(k,p,z,B);e(k)&&D.addRect(k)};this.resetTransform=function(){this.Td.transform(this.Vd);var k=a.Matrix.invert(this.Vd);this.Od.concat(k);this.Vd=this.Od.getTotalMatrix()};this.restore=function(){var k=this.mf.pop();if(k){var p=a.Matrix.multiply(this.Vd,a.Matrix.invert(k.Hf));this.Td.transform(p);this.Rd.delete();this.Rd=k.cg;this.we=k.ag;this.Le=k.pg;this.he=k.og;this.ce=k.fs;this.ye=k.mg;this.ze=k.ng;this.xe=k.gg;this.Ke=k.lg;this.le=k.Pf;this.Qd=k.Qf;this.Je=k.bg;
this.Re=k.Of;this.Od.restore();this.Vd=this.Od.getTotalMatrix()}};this.rotate=function(k){if(isFinite(k)){var p=a.Matrix.rotated(-k);this.Td.transform(p);this.Od.rotate(k/Math.PI*180,0,0);this.Vd=this.Od.getTotalMatrix()}};this.save=function(){if(this.ce.ue){var k=this.ce.ue();this.De.push(k)}else k=this.ce;if(this.he.ue){var p=this.he.ue();this.De.push(p)}else p=this.he;this.mf.push({Hf:this.Vd.slice(),ag:this.we.slice(),pg:this.Le,og:p,fs:k,mg:this.ye,ng:this.ze,gg:this.xe,lg:this.Ke,Pf:this.le,
bg:this.Je,Qf:this.Qd,cg:this.Rd.copy(),Of:this.Re});this.Od.save()};this.scale=function(k,p){if(e(arguments)){var z=a.Matrix.scaled(1/k,1/p);this.Td.transform(z);this.Od.scale(k,p);this.Vd=this.Od.getTotalMatrix()}};this.setLineDash=function(k){for(var p=0;p<k.length;p++)if(!isFinite(k[p])||0>k[p])return;1===k.length%2&&Array.prototype.push.apply(k,k);this.we=k};this.setTransform=function(k,p,z,B,D,F){e(arguments)&&(this.resetTransform(),this.transform(k,p,z,B,D,F))};this.te=function(){var k=a.Matrix.invert(this.Vd);
this.Od.concat(k);this.Od.concat(a.Matrix.translated(this.ye,this.ze));this.Od.concat(this.Vd)};this.Ae=function(k){var p=a.multiplyByAlpha(this.Ke,this.le);if(!a.getColorComponents(p)[3]||!(this.xe||this.ze||this.ye))return null;k=k.copy();k.setColor(p);var z=a.MaskFilter.MakeBlur(a.BlurStyle.Normal,this.xe/2,!1);k.setMaskFilter(z);k.dispose=function(){z.delete();this.delete()};return k};this.bf=function(){var k=this.Rd.copy();k.setStyle(a.PaintStyle.Stroke);if(f(this.he)){var p=a.multiplyByAlpha(this.he,
this.le);k.setColor(p)}else p=this.he.ve(this.Vd),k.setColor(a.Color(0,0,0,this.le)),k.setShader(p);k.setStrokeWidth(this.Le);if(this.we.length){var z=a.PathEffect.MakeDash(this.we,this.Je);k.setPathEffect(z)}k.dispose=function(){z&&z.delete();this.delete()};return k};this.stroke=function(k){k=k?k.Xd:this.Td;var p=this.bf(),z=this.Ae(p);z&&(this.Od.save(),this.te(),this.Od.drawPath(k,z),this.Od.restore(),z.dispose());this.Od.drawPath(k,p);p.dispose()};this.strokeRect=function(k,p,z,B){var D=this.bf(),
F=this.Ae(D);F&&(this.Od.save(),this.te(),this.Od.drawRect(a.XYWHRect(k,p,z,B),F),this.Od.restore(),F.dispose());this.Od.drawRect(a.XYWHRect(k,p,z,B),D);D.dispose()};this.strokeText=function(k,p,z){var B=this.bf();k=a.TextBlob.MakeFromText(k,this.oe);var D=this.Ae(B);D&&(this.Od.save(),this.te(),this.Od.drawTextBlob(k,p,z,D),this.Od.restore(),D.dispose());this.Od.drawTextBlob(k,p,z,B);k.delete();B.dispose()};this.translate=function(k,p){if(e(arguments)){var z=a.Matrix.translated(-k,-p);this.Td.transform(z);
this.Od.translate(k,p);this.Vd=this.Od.getTotalMatrix()}};this.transform=function(k,p,z,B,D,F){k=[k,z,D,p,B,F,0,0,1];p=a.Matrix.invert(k);this.Td.transform(p);this.Od.concat(k);this.Vd=this.Od.getTotalMatrix()};this.addHitRegion=function(){};this.clearHitRegions=function(){};this.drawFocusIfNeeded=function(){};this.removeHitRegion=function(){};this.scrollPathIntoView=function(){};Object.defineProperty(this,"canvas",{value:null,writable:!1})}function y(G){this.cf=G;this.Nd=new r(G.getCanvas());this.Se=
[];this.decodeImage=function(k){k=a.MakeImageFromEncoded(k);if(!k)throw"Invalid input";this.Se.push(k);return new E(k)};this.loadFont=function(k,p){k=a.Typeface.MakeFreeTypeFaceFromData(k);if(!k)return null;this.Se.push(k);var z=(p.style||"normal")+"|"+(p.variant||"normal")+"|"+(p.weight||"normal");p=p.family;ea[p]||(ea[p]={"*":k});ea[p][z]=k};this.makePath2D=function(k){k=new aa(k);this.Se.push(k.Xd);return k};this.getContext=function(k){return"2d"===k?this.Nd:null};this.toDataURL=function(k,p){this.cf.flush();
var z=this.cf.makeImageSnapshot();if(z){k=k||"image/png";var B=a.ImageFormat.PNG;"image/jpeg"===k&&(B=a.ImageFormat.JPEG);if(p=z.encodeToBytes(B,p||.92)){z.delete();k="data:"+k+";base64,";if("undefined"!==typeof Buffer)p=Buffer.from(p).toString("base64");else{z=0;B=p.length;for(var D="",F;z<B;)F=p.slice(z,Math.min(z+32768,B)),D+=String.fromCharCode.apply(null,F),z+=32768;p=btoa(D)}return k+p}}};this.dispose=function(){this.Nd.ne();this.Se.forEach(function(k){k.delete()});this.cf.dispose()}}function E(G){this.width=
G.width();this.height=G.height();this.naturalWidth=this.width;this.naturalHeight=this.height;this.tf=function(){return G}}function K(G,k,p){if(!k||0===p)throw"invalid dimensions, width and height must be non-zero";if(G.length%4)throw"arr must be a multiple of 4";p=p||G.length/(4*k);Object.defineProperty(this,"data",{value:G,writable:!1});Object.defineProperty(this,"height",{value:p,writable:!1});Object.defineProperty(this,"width",{value:k,writable:!1})}function O(G,k,p,z){this.Zd=null;this.ee=[];
this.be=[];this.addColorStop=function(B,D){if(0>B||1<B||!isFinite(B))throw"offset must be between 0 and 1 inclusively";D=g(D);var F=this.be.indexOf(B);if(-1!==F)this.ee[F]=D;else{for(F=0;F<this.be.length&&!(this.be[F]>B);F++);this.be.splice(F,0,B);this.ee.splice(F,0,D)}};this.ue=function(){var B=new O(G,k,p,z);B.ee=this.ee.slice();B.be=this.be.slice();return B};this.ne=function(){this.Zd&&(this.Zd.delete(),this.Zd=null)};this.ve=function(B){var D=[G,k,p,z];a.Matrix.mapPoints(B,D);B=D[0];var F=D[1],
L=D[2];D=D[3];this.ne();return this.Zd=a.Shader.MakeLinearGradient([B,F],[L,D],this.ee,this.be,a.TileMode.Clamp)}}function Q(G,k,p,z,B,D){if(e([k,p,z,B,D])){if(0>D)throw"radii cannot be negative";G.isEmpty()&&G.moveTo(k,p);G.arcToTangent(k,p,z,B,D)}}function W(G){if(!G.isEmpty()){var k=G.getBounds();(k[3]-k[1]||k[2]-k[0])&&G.close()}}function u(G,k,p,z,B,D,F){F=(F-D)/Math.PI*180;D=D/Math.PI*180;k=a.LTRBRect(k-z,p-B,k+z,p+B);1E-5>Math.abs(Math.abs(F)-360)?(p=F/2,G.arcToOval(k,D,p,!1),G.arcToOval(k,
D+p,p,!1)):G.arcToOval(k,D,F,!1)}function I(G,k,p,z,B,D,F,L,ba){if(e([k,p,z,B,D,F,L])){if(0>z||0>B)throw"radii cannot be negative";var ca=2*Math.PI,Ia=F%ca;0>Ia&&(Ia+=ca);var ab=Ia-F;F=Ia;L+=ab;!ba&&L-F>=ca?L=F+ca:ba&&F-L>=ca?L=F-ca:!ba&&F>L?L=F+(ca-(F-L)%ca):ba&&F<L&&(L=F-(ca-(L-F)%ca));D?(ba=a.Matrix.rotated(D,k,p),D=a.Matrix.rotated(-D,k,p),G.transform(D),u(G,k,p,z,B,F,L),G.transform(ba)):u(G,k,p,z,B,F,L)}}function R(G,k,p){e([k,p])&&(G.isEmpty()&&G.moveTo(k,p),G.lineTo(k,p))}function aa(G){this.Xd=
null;this.Xd="string"===typeof G?a.Path.MakeFromSVGString(G):G&&G.$e?G.Xd.copy():new a.Path;this.$e=function(){return this.Xd};this.addPath=function(k,p){p||(p={a:1,c:0,e:0,b:0,d:1,f:0});this.Xd.addPath(k.Xd,[p.a,p.c,p.e,p.b,p.d,p.f])};this.arc=function(k,p,z,B,D,F){I(this.Xd,k,p,z,z,0,B,D,F)};this.arcTo=function(k,p,z,B,D){Q(this.Xd,k,p,z,B,D)};this.bezierCurveTo=function(k,p,z,B,D,F){var L=this.Xd;e([k,p,z,B,D,F])&&(L.isEmpty()&&L.moveTo(k,p),L.cubicTo(k,p,z,B,D,F))};this.closePath=function(){W(this.Xd)};
this.ellipse=function(k,p,z,B,D,F,L,ba){I(this.Xd,k,p,z,B,D,F,L,ba)};this.lineTo=function(k,p){R(this.Xd,k,p)};this.moveTo=function(k,p){var z=this.Xd;e([k,p])&&z.moveTo(k,p)};this.quadraticCurveTo=function(k,p,z,B){var D=this.Xd;e([k,p,z,B])&&(D.isEmpty()&&D.moveTo(k,p),D.quadTo(k,p,z,B))};this.rect=function(k,p,z,B){var D=this.Xd;k=a.XYWHRect(k,p,z,B);e(k)&&D.addRect(k)}}function ja(G,k){this.Zd=null;G instanceof E&&(G=G.tf());this.Cf=G;this._transform=a.Matrix.identity();""===k&&(k="repeat");switch(k){case "repeat-x":this.Be=
a.TileMode.Repeat;this.Ce=a.TileMode.Decal;break;case "repeat-y":this.Be=a.TileMode.Decal;this.Ce=a.TileMode.Repeat;break;case "repeat":this.Ce=this.Be=a.TileMode.Repeat;break;case "no-repeat":this.Ce=this.Be=a.TileMode.Decal;break;default:throw"invalid repetition mode "+k;}this.setTransform=function(p){p=[p.a,p.c,p.e,p.b,p.d,p.f,0,0,1];e(p)&&(this._transform=p)};this.ue=function(){var p=new ja;p.Be=this.Be;p.Ce=this.Ce;return p};this.ne=function(){this.Zd&&(this.Zd.delete(),this.Zd=null)};this.ve=
function(){this.ne();return this.Zd=this.Cf.makeShaderCubic(this.Be,this.Ce,1/3,1/3,this._transform)}}function qa(G,k,p,z,B,D){this.Zd=null;this.ee=[];this.be=[];this.addColorStop=function(F,L){if(0>F||1<F||!isFinite(F))throw"offset must be between 0 and 1 inclusively";L=g(L);var ba=this.be.indexOf(F);if(-1!==ba)this.ee[ba]=L;else{for(ba=0;ba<this.be.length&&!(this.be[ba]>F);ba++);this.be.splice(ba,0,F);this.ee.splice(ba,0,L)}};this.ue=function(){var F=new qa(G,k,p,z,B,D);F.ee=this.ee.slice();F.be=
this.be.slice();return F};this.ne=function(){this.Zd&&(this.Zd.delete(),this.Zd=null)};this.ve=function(F){var L=[G,k,z,B];a.Matrix.mapPoints(F,L);var ba=L[0],ca=L[1],Ia=L[2];L=L[3];var ab=(Math.abs(F[0])+Math.abs(F[4]))/2;F=p*ab;ab*=D;this.ne();return this.Zd=a.Shader.MakeTwoPointConicalGradient([ba,ca],F,[Ia,L],ab,this.ee,this.be,a.TileMode.Clamp)}}a._testing={};var ua={aliceblue:Float32Array.of(.941,.973,1,1),antiquewhite:Float32Array.of(.98,.922,.843,1),aqua:Float32Array.of(0,1,1,1),aquamarine:Float32Array.of(.498,
1,.831,1),azure:Float32Array.of(.941,1,1,1),beige:Float32Array.of(.961,.961,.863,1),bisque:Float32Array.of(1,.894,.769,1),black:Float32Array.of(0,0,0,1),blanchedalmond:Float32Array.of(1,.922,.804,1),blue:Float32Array.of(0,0,1,1),blueviolet:Float32Array.of(.541,.169,.886,1),brown:Float32Array.of(.647,.165,.165,1),burlywood:Float32Array.of(.871,.722,.529,1),cadetblue:Float32Array.of(.373,.62,.627,1),chartreuse:Float32Array.of(.498,1,0,1),chocolate:Float32Array.of(.824,.412,.118,1),coral:Float32Array.of(1,
.498,.314,1),cornflowerblue:Float32Array.of(.392,.584,.929,1),cornsilk:Float32Array.of(1,.973,.863,1),crimson:Float32Array.of(.863,.078,.235,1),cyan:Float32Array.of(0,1,1,1),darkblue:Float32Array.of(0,0,.545,1),darkcyan:Float32Array.of(0,.545,.545,1),darkgoldenrod:Float32Array.of(.722,.525,.043,1),darkgray:Float32Array.of(.663,.663,.663,1),darkgreen:Float32Array.of(0,.392,0,1),darkgrey:Float32Array.of(.663,.663,.663,1),darkkhaki:Float32Array.of(.741,.718,.42,1),darkmagenta:Float32Array.of(.545,0,
.545,1),darkolivegreen:Float32Array.of(.333,.42,.184,1),darkorange:Float32Array.of(1,.549,0,1),darkorchid:Float32Array.of(.6,.196,.8,1),darkred:Float32Array.of(.545,0,0,1),darksalmon:Float32Array.of(.914,.588,.478,1),darkseagreen:Float32Array.of(.561,.737,.561,1),darkslateblue:Float32Array.of(.282,.239,.545,1),darkslategray:Float32Array.of(.184,.31,.31,1),darkslategrey:Float32Array.of(.184,.31,.31,1),darkturquoise:Float32Array.of(0,.808,.82,1),darkviolet:Float32Array.of(.58,0,.827,1),deeppink:Float32Array.of(1,
.078,.576,1),deepskyblue:Float32Array.of(0,.749,1,1),dimgray:Float32Array.of(.412,.412,.412,1),dimgrey:Float32Array.of(.412,.412,.412,1),dodgerblue:Float32Array.of(.118,.565,1,1),firebrick:Float32Array.of(.698,.133,.133,1),floralwhite:Float32Array.of(1,.98,.941,1),forestgreen:Float32Array.of(.133,.545,.133,1),fuchsia:Float32Array.of(1,0,1,1),gainsboro:Float32Array.of(.863,.863,.863,1),ghostwhite:Float32Array.of(.973,.973,1,1),gold:Float32Array.of(1,.843,0,1),goldenrod:Float32Array.of(.855,.647,.125,
1),gray:Float32Array.of(.502,.502,.502,1),green:Float32Array.of(0,.502,0,1),greenyellow:Float32Array.of(.678,1,.184,1),grey:Float32Array.of(.502,.502,.502,1),honeydew:Float32Array.of(.941,1,.941,1),hotpink:Float32Array.of(1,.412,.706,1),indianred:Float32Array.of(.804,.361,.361,1),indigo:Float32Array.of(.294,0,.51,1),ivory:Float32Array.of(1,1,.941,1),khaki:Float32Array.of(.941,.902,.549,1),lavender:Float32Array.of(.902,.902,.98,1),lavenderblush:Float32Array.of(1,.941,.961,1),lawngreen:Float32Array.of(.486,
.988,0,1),lemonchiffon:Float32Array.of(1,.98,.804,1),lightblue:Float32Array.of(.678,.847,.902,1),lightcoral:Float32Array.of(.941,.502,.502,1),lightcyan:Float32Array.of(.878,1,1,1),lightgoldenrodyellow:Float32Array.of(.98,.98,.824,1),lightgray:Float32Array.of(.827,.827,.827,1),lightgreen:Float32Array.of(.565,.933,.565,1),lightgrey:Float32Array.of(.827,.827,.827,1),lightpink:Float32Array.of(1,.714,.757,1),lightsalmon:Float32Array.of(1,.627,.478,1),lightseagreen:Float32Array.of(.125,.698,.667,1),lightskyblue:Float32Array.of(.529,
.808,.98,1),lightslategray:Float32Array.of(.467,.533,.6,1),lightslategrey:Float32Array.of(.467,.533,.6,1),lightsteelblue:Float32Array.of(.69,.769,.871,1),lightyellow:Float32Array.of(1,1,.878,1),lime:Float32Array.of(0,1,0,1),limegreen:Float32Array.of(.196,.804,.196,1),linen:Float32Array.of(.98,.941,.902,1),magenta:Float32Array.of(1,0,1,1),maroon:Float32Array.of(.502,0,0,1),mediumaquamarine:Float32Array.of(.4,.804,.667,1),mediumblue:Float32Array.of(0,0,.804,1),mediumorchid:Float32Array.of(.729,.333,
.827,1),mediumpurple:Float32Array.of(.576,.439,.859,1),mediumseagreen:Float32Array.of(.235,.702,.443,1),mediumslateblue:Float32Array.of(.482,.408,.933,1),mediumspringgreen:Float32Array.of(0,.98,.604,1),mediumturquoise:Float32Array.of(.282,.82,.8,1),mediumvioletred:Float32Array.of(.78,.082,.522,1),midnightblue:Float32Array.of(.098,.098,.439,1),mintcream:Float32Array.of(.961,1,.98,1),mistyrose:Float32Array.of(1,.894,.882,1),moccasin:Float32Array.of(1,.894,.71,1),navajowhite:Float32Array.of(1,.871,.678,
1),navy:Float32Array.of(0,0,.502,1),oldlace:Float32Array.of(.992,.961,.902,1),olive:Float32Array.of(.502,.502,0,1),olivedrab:Float32Array.of(.42,.557,.137,1),orange:Float32Array.of(1,.647,0,1),orangered:Float32Array.of(1,.271,0,1),orchid:Float32Array.of(.855,.439,.839,1),palegoldenrod:Float32Array.of(.933,.91,.667,1),palegreen:Float32Array.of(.596,.984,.596,1),paleturquoise:Float32Array.of(.686,.933,.933,1),palevioletred:Float32Array.of(.859,.439,.576,1),papayawhip:Float32Array.of(1,.937,.835,1),
peachpuff:Float32Array.of(1,.855,.725,1),peru:Float32Array.of(.804,.522,.247,1),pink:Float32Array.of(1,.753,.796,1),plum:Float32Array.of(.867,.627,.867,1),powderblue:Float32Array.of(.69,.878,.902,1),purple:Float32Array.of(.502,0,.502,1),rebeccapurple:Float32Array.of(.4,.2,.6,1),red:Float32Array.of(1,0,0,1),rosybrown:Float32Array.of(.737,.561,.561,1),royalblue:Float32Array.of(.255,.412,.882,1),saddlebrown:Float32Array.of(.545,.271,.075,1),salmon:Float32Array.of(.98,.502,.447,1),sandybrown:Float32Array.of(.957,
.643,.376,1),seagreen:Float32Array.of(.18,.545,.341,1),seashell:Float32Array.of(1,.961,.933,1),sienna:Float32Array.of(.627,.322,.176,1),silver:Float32Array.of(.753,.753,.753,1),skyblue:Float32Array.of(.529,.808,.922,1),slateblue:Float32Array.of(.416,.353,.804,1),slategray:Float32Array.of(.439,.502,.565,1),slategrey:Float32Array.of(.439,.502,.565,1),snow:Float32Array.of(1,.98,.98,1),springgreen:Float32Array.of(0,1,.498,1),steelblue:Float32Array.of(.275,.51,.706,1),tan:Float32Array.of(.824,.706,.549,
1),teal:Float32Array.of(0,.502,.502,1),thistle:Float32Array.of(.847,.749,.847,1),tomato:Float32Array.of(1,.388,.278,1),transparent:Float32Array.of(0,0,0,0),turquoise:Float32Array.of(.251,.878,.816,1),violet:Float32Array.of(.933,.51,.933,1),wheat:Float32Array.of(.961,.871,.702,1),white:Float32Array.of(1,1,1,1),whitesmoke:Float32Array.of(.961,.961,.961,1),yellow:Float32Array.of(1,1,0,1),yellowgreen:Float32Array.of(.604,.804,.196,1)};a._testing.parseColor=g;a._testing.colorToString=d;var Aa=RegExp("(italic|oblique|normal|)\\s*(small-caps|normal|)\\s*(bold|bolder|lighter|[1-9]00|normal|)\\s*([\\d\\.]+)(px|pt|pc|in|cm|mm|%|em|ex|ch|rem|q)(.+)"),
ea={"Noto Mono":{"*":null},monospace:{"*":null}};a._testing.parseFontString=l;a.MakeCanvas=function(G,k){return(G=a.MakeSurface(G,k))?new y(G):null};a.ImageData=function(){if(2===arguments.length){var G=arguments[0],k=arguments[1];return new K(new Uint8ClampedArray(4*G*k),G,k)}if(3===arguments.length){var p=arguments[0];if(p.prototype.constructor!==Uint8ClampedArray)throw"bytes must be given as a Uint8ClampedArray";G=arguments[1];k=arguments[2];if(p%4)throw"bytes must be given in a multiple of 4";
if(p%G)throw"bytes must divide evenly by width";if(k&&k!==p/(4*G))throw"invalid height given";return new K(p,G,p/(4*G))}throw"invalid number of arguments - takes 2 or 3, saw "+arguments.length;}})()})(v);var sa=Object.assign({},v),va="./this.program",wa=(a,b)=>{throw b;},xa="object"==typeof window,ya="function"==typeof importScripts,za="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,Ba="",Ca,Da,Ea,fs,Fa,Ga;
if(za)Ba=ya?require("path").dirname(Ba)+"/":__dirname+"/",Ga=()=>{Fa||(fs=require("fs"),Fa=require("path"))},Ca=function(a,b){Ga();a=Fa.normalize(a);return fs.readFileSync(a,b?void 0:"utf8")},Ea=a=>{a=Ca(a,!0);a.buffer||(a=new Uint8Array(a));return a},Da=(a,b,c)=>{Ga();a=Fa.normalize(a);fs.readFile(a,function(f,h){f?c(f):b(h.buffer)})},1<process.argv.length&&(va=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("unhandledRejection",function(a){throw a;}),wa=(a,b)=>{if(noExitRuntime)throw process.exitCode=
a,b;b instanceof Ja||Ka("exiting due to exception: "+b);process.exit(a)},v.inspect=function(){return"[Emscripten Module object]"};else if(xa||ya)ya?Ba=self.location.href:"undefined"!=typeof document&&document.currentScript&&(Ba=document.currentScript.src),_scriptDir&&(Ba=_scriptDir),0!==Ba.indexOf("blob:")?Ba=Ba.substr(0,Ba.replace(/[?#].*/,"").lastIndexOf("/")+1):Ba="",Ca=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.send(null);return b.responseText},ya&&(Ea=a=>{var b=new XMLHttpRequest;b.open("GET",
a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)}),Da=(a,b,c)=>{var f=new XMLHttpRequest;f.open("GET",a,!0);f.responseType="arraybuffer";f.onload=()=>{200==f.status||0==f.status&&f.response?b(f.response):c()};f.onerror=c;f.send(null)};var La=v.print||console.log.bind(console),Ka=v.printErr||console.warn.bind(console);Object.assign(v,sa);sa=null;v.thisProgram&&(va=v.thisProgram);v.quit&&(wa=v.quit);var Ma=0,Na;v.wasmBinary&&(Na=v.wasmBinary);
var noExitRuntime=v.noExitRuntime||!0;"object"!=typeof WebAssembly&&Pa("no native wasm support detected");var Qa,Ra=!1,Sa="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;
function Ua(a,b,c){var f=b+c;for(c=b;a[c]&&!(c>=f);)++c;if(16<c-b&&a.buffer&&Sa)return Sa.decode(a.subarray(b,c));for(f="";b<c;){var h=a[b++];if(h&128){var m=a[b++]&63;if(192==(h&224))f+=String.fromCharCode((h&31)<<6|m);else{var t=a[b++]&63;h=224==(h&240)?(h&15)<<12|m<<6|t:(h&7)<<18|m<<12|t<<6|a[b++]&63;65536>h?f+=String.fromCharCode(h):(h-=65536,f+=String.fromCharCode(55296|h>>10,56320|h&1023))}}else f+=String.fromCharCode(h)}return f}function Va(a,b){return a?Ua(J,a,b):""}
function ra(a,b,c,f){if(!(0<f))return 0;var h=c;f=c+f-1;for(var m=0;m<a.length;++m){var t=a.charCodeAt(m);if(55296<=t&&57343>=t){var n=a.charCodeAt(++m);t=65536+((t&1023)<<10)|n&1023}if(127>=t){if(c>=f)break;b[c++]=t}else{if(2047>=t){if(c+1>=f)break;b[c++]=192|t>>6}else{if(65535>=t){if(c+2>=f)break;b[c++]=224|t>>12}else{if(c+3>=f)break;b[c++]=240|t>>18;b[c++]=128|t>>12&63}b[c++]=128|t>>6&63}b[c++]=128|t&63}}b[c]=0;return c-h}
function oa(a){for(var b=0,c=0;c<a.length;++c){var f=a.charCodeAt(c);55296<=f&&57343>=f&&(f=65536+((f&1023)<<10)|a.charCodeAt(++c)&1023);127>=f?++b:b=2047>=f?b+2:65535>=f?b+3:b+4}return b}var Wa="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function Xa(a,b){var c=a>>1;for(var f=c+b/2;!(c>=f)&&Ya[c];)++c;c<<=1;if(32<c-a&&Wa)return Wa.decode(J.subarray(a,c));c="";for(f=0;!(f>=b/2);++f){var h=Za[a+2*f>>1];if(0==h)break;c+=String.fromCharCode(h)}return c}
function bb(a,b,c){void 0===c&&(c=2147483647);if(2>c)return 0;c-=2;var f=b;c=c<2*a.length?c/2:a.length;for(var h=0;h<c;++h)Za[b>>1]=a.charCodeAt(h),b+=2;Za[b>>1]=0;return b-f}function cb(a){return 2*a.length}function db(a,b){for(var c=0,f="";!(c>=b/4);){var h=P[a+4*c>>2];if(0==h)break;++c;65536<=h?(h-=65536,f+=String.fromCharCode(55296|h>>10,56320|h&1023)):f+=String.fromCharCode(h)}return f}
function eb(a,b,c){void 0===c&&(c=2147483647);if(4>c)return 0;var f=b;c=f+c-4;for(var h=0;h<a.length;++h){var m=a.charCodeAt(h);if(55296<=m&&57343>=m){var t=a.charCodeAt(++h);m=65536+((m&1023)<<10)|t&1023}P[b>>2]=m;b+=4;if(b+4>c)break}P[b>>2]=0;return b-f}function jb(a){for(var b=0,c=0;c<a.length;++c){var f=a.charCodeAt(c);55296<=f&&57343>=f&&++c;b+=4}return b}var kb,lb,J,Za,Ya,P,mb,U,nb;
function ob(){var a=Qa.buffer;kb=a;v.HEAP8=lb=new Int8Array(a);v.HEAP16=Za=new Int16Array(a);v.HEAP32=P=new Int32Array(a);v.HEAPU8=J=new Uint8Array(a);v.HEAPU16=Ya=new Uint16Array(a);v.HEAPU32=mb=new Uint32Array(a);v.HEAPF32=U=new Float32Array(a);v.HEAPF64=nb=new Float64Array(a)}var pb,qb=[],rb=[],sb=[];function tb(){var a=v.preRun.shift();qb.unshift(a)}var ub=0,vb=null,wb=null;
function Pa(a){if(v.onAbort)v.onAbort(a);a="Aborted("+a+")";Ka(a);Ra=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");fa(a);throw a;}function yb(){return zb.startsWith("data:application/octet-stream;base64,")}var zb;zb="canvaskit.wasm";if(!yb()){var Ab=zb;zb=v.locateFile?v.locateFile(Ab,Ba):Ba+Ab}function Bb(){var a=zb;try{if(a==zb&&Na)return new Uint8Array(Na);if(Ea)return Ea(a);throw"both async and sync fetching of the wasm failed";}catch(b){Pa(b)}}
function Cb(){if(!Na&&(xa||ya)){if("function"==typeof fetch&&!zb.startsWith("file://"))return fetch(zb,{credentials:"same-origin"}).then(function(a){if(!a.ok)throw"failed to load wasm binary file at '"+zb+"'";return a.arrayBuffer()}).catch(function(){return Bb()});if(Da)return new Promise(function(a,b){Da(zb,function(c){a(new Uint8Array(c))},b)})}return Promise.resolve().then(function(){return Bb()})}
function Db(a){for(;0<a.length;){var b=a.shift();if("function"==typeof b)b(v);else{var c=b.xg;"number"==typeof c?void 0===b.df?Eb(c)():Eb(c)(b.df):c(void 0===b.df?null:b.df)}}}function Eb(a){return pb.get(a)}var Fb=[null,[],[]],Gb={},Hb={};function Ib(a){for(;a.length;){var b=a.pop();a.pop()(b)}}function Jb(a){return this.fromWireType(mb[a>>2])}var Kb={},Lb={},Mb={};function Nb(a){if(void 0===a)return"_unknown";a=a.replace(/[^a-zA-Z0-9_]/g,"$");var b=a.charCodeAt(0);return 48<=b&&57>=b?"_"+a:a}
function Ob(a,b){a=Nb(a);return function(){null;return b.apply(this,arguments)}}function Pb(a){var b=Error,c=Ob(a,function(f){this.name=a;this.message=f;f=Error(f).stack;void 0!==f&&(this.stack=this.toString()+"\n"+f.replace(/^Error(:[^\n]*)?\n/,""))});c.prototype=Object.create(b.prototype);c.prototype.constructor=c;c.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message};return c}var Qb=void 0;function Rb(a){throw new Qb(a);}
function Sb(a,b,c){function f(n){n=c(n);n.length!==a.length&&Rb("Mismatched type converter count");for(var q=0;q<a.length;++q)Tb(a[q],n[q])}a.forEach(function(n){Mb[n]=b});var h=Array(b.length),m=[],t=0;b.forEach((n,q)=>{Lb.hasOwnProperty(n)?h[q]=Lb[n]:(m.push(n),Kb.hasOwnProperty(n)||(Kb[n]=[]),Kb[n].push(()=>{h[q]=Lb[n];++t;t===m.length&&f(h)}))});0===m.length&&f(h)}
function Ub(a){switch(a){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+a);}}var bc=void 0;function cc(a){for(var b="";J[a];)b+=bc[J[a++]];return b}var dc=void 0;function X(a){throw new dc(a);}
function Tb(a,b,c={}){if(!("argPackAdvance"in b))throw new TypeError("registerType registeredInstance requires argPackAdvance");var f=b.name;a||X('type "'+f+'" must have a positive integer typeid pointer');if(Lb.hasOwnProperty(a)){if(c.Yf)return;X("Cannot register type '"+f+"' twice")}Lb[a]=b;delete Mb[a];Kb.hasOwnProperty(a)&&(b=Kb[a],delete Kb[a],b.forEach(h=>h()))}function ec(a){X(a.Md.Yd.Sd.name+" instance already deleted")}var fc=!1;function gc(){}
function hc(a){--a.count.value;0===a.count.value&&(a.ae?a.ge.me(a.ae):a.Yd.Sd.me(a.Ud))}function ic(a,b,c){if(b===c)return a;if(void 0===c.ie)return null;a=ic(a,b,c.ie);return null===a?null:c.Lf(a)}var jc={},kc=[];function lc(){for(;kc.length;){var a=kc.pop();a.Md.Ge=!1;a["delete"]()}}var mc=void 0,nc={};function oc(a,b){for(void 0===b&&X("ptr should not be undefined");a.ie;)b=a.Pe(b),a=a.ie;return nc[b]}
function pc(a,b){b.Yd&&b.Ud||Rb("makeClassHandle requires ptr and ptrType");!!b.ge!==!!b.ae&&Rb("Both smartPtrType and smartPtr must be specified");b.count={value:1};return qc(Object.create(a,{Md:{value:b}}))}function qc(a){if("undefined"===typeof FinalizationRegistry)return qc=b=>b,a;fc=new FinalizationRegistry(b=>{hc(b.Md)});qc=b=>{var c=b.Md;c.ae&&fc.register(b,{Md:c},b);return b};gc=b=>{fc.unregister(b)};return qc(a)}function rc(){}
function sc(a,b,c){if(void 0===a[b].$d){var f=a[b];a[b]=function(){a[b].$d.hasOwnProperty(arguments.length)||X("Function '"+c+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+a[b].$d+")!");return a[b].$d[arguments.length].apply(this,arguments)};a[b].$d=[];a[b].$d[f.Ee]=f}}
function tc(a,b,c){v.hasOwnProperty(a)?((void 0===c||void 0!==v[a].$d&&void 0!==v[a].$d[c])&&X("Cannot register public name '"+a+"' twice"),sc(v,a,a),v.hasOwnProperty(c)&&X("Cannot register multiple overloads of a function with the same number of arguments ("+c+")!"),v[a].$d[c]=b):(v[a]=b,void 0!==c&&(v[a].zg=c))}function uc(a,b,c,f,h,m,t,n){this.name=a;this.constructor=b;this.He=c;this.me=f;this.ie=h;this.Rf=m;this.Pe=t;this.Lf=n;this.eg=[]}
function vc(a,b,c){for(;b!==c;)b.Pe||X("Expected null or instance of "+c.name+", got an instance of "+b.name),a=b.Pe(a),b=b.ie;return a}function wc(a,b){if(null===b)return this.ff&&X("null is not a valid "+this.name),0;b.Md||X('Cannot pass "'+xc(b)+'" as a '+this.name);b.Md.Ud||X("Cannot pass deleted object as a pointer of type "+this.name);return vc(b.Md.Ud,b.Md.Yd.Sd,this.Sd)}
function yc(a,b){if(null===b){this.ff&&X("null is not a valid "+this.name);if(this.Ue){var c=this.gf();null!==a&&a.push(this.me,c);return c}return 0}b.Md||X('Cannot pass "'+xc(b)+'" as a '+this.name);b.Md.Ud||X("Cannot pass deleted object as a pointer of type "+this.name);!this.Te&&b.Md.Yd.Te&&X("Cannot convert argument of type "+(b.Md.ge?b.Md.ge.name:b.Md.Yd.name)+" to parameter type "+this.name);c=vc(b.Md.Ud,b.Md.Yd.Sd,this.Sd);if(this.Ue)switch(void 0===b.Md.ae&&X("Passing raw pointer to smart pointer is illegal"),
this.kg){case 0:b.Md.ge===this?c=b.Md.ae:X("Cannot convert argument of type "+(b.Md.ge?b.Md.ge.name:b.Md.Yd.name)+" to parameter type "+this.name);break;case 1:c=b.Md.ae;break;case 2:if(b.Md.ge===this)c=b.Md.ae;else{var f=b.clone();c=this.fg(c,zc(function(){f["delete"]()}));null!==a&&a.push(this.me,c)}break;default:X("Unsupporting sharing policy")}return c}
function Ac(a,b){if(null===b)return this.ff&&X("null is not a valid "+this.name),0;b.Md||X('Cannot pass "'+xc(b)+'" as a '+this.name);b.Md.Ud||X("Cannot pass deleted object as a pointer of type "+this.name);b.Md.Yd.Te&&X("Cannot convert argument of type "+b.Md.Yd.name+" to parameter type "+this.name);return vc(b.Md.Ud,b.Md.Yd.Sd,this.Sd)}
function Bc(a,b,c,f,h,m,t,n,q,x,C){this.name=a;this.Sd=b;this.ff=c;this.Te=f;this.Ue=h;this.dg=m;this.kg=t;this.vf=n;this.gf=q;this.fg=x;this.me=C;h||void 0!==b.ie?this.toWireType=yc:(this.toWireType=f?wc:Ac,this.fe=null)}function Ic(a,b,c){v.hasOwnProperty(a)||Rb("Replacing nonexistant public symbol");void 0!==v[a].$d&&void 0!==c?v[a].$d[c]=b:(v[a]=b,v[a].Ee=c)}
function Jc(a,b){var c=[];return function(){c.length=0;Object.assign(c,arguments);if(a.includes("j")){var f=v["dynCall_"+a];f=c&&c.length?f.apply(null,[b].concat(c)):f.call(null,b)}else f=Eb(b).apply(null,c);return f}}function Kc(a,b){a=cc(a);var c=a.includes("j")?Jc(a,b):Eb(b);"function"!=typeof c&&X("unknown function pointer with signature "+a+": "+b);return c}var Lc=void 0;function Mc(a){a=Nc(a);var b=cc(a);Oc(a);return b}
function Pc(a,b){function c(m){h[m]||Lb[m]||(Mb[m]?Mb[m].forEach(c):(f.push(m),h[m]=!0))}var f=[],h={};b.forEach(c);throw new Lc(a+": "+f.map(Mc).join([", "]));}
function Qc(a,b,c,f,h){var m=b.length;2>m&&X("argTypes array size mismatch! Must at least get return value and 'this' types!");var t=null!==b[1]&&null!==c,n=!1;for(c=1;c<b.length;++c)if(null!==b[c]&&void 0===b[c].fe){n=!0;break}var q="void"!==b[0].name,x=m-2,C=Array(x),H=[],M=[];return function(){arguments.length!==x&&X("function "+a+" called with "+arguments.length+" arguments, expected "+x+" args!");M.length=0;H.length=t?2:1;H[0]=h;if(t){var A=b[1].toWireType(M,this);H[1]=A}for(var N=0;N<x;++N)C[N]=
b[N+2].toWireType(M,arguments[N]),H.push(C[N]);N=f.apply(null,H);if(n)Ib(M);else for(var S=t?1:2;S<b.length;S++){var T=1===S?A:C[S-2];null!==b[S].fe&&b[S].fe(T)}A=q?b[0].fromWireType(N):void 0;return A}}function Rc(a,b){for(var c=[],f=0;f<a;f++)c.push(P[(b>>2)+f]);return c}var Sc=[],Tc=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Uc(a){4<a&&0===--Tc[a].hf&&(Tc[a]=void 0,Sc.push(a))}
var Vc=a=>{a||X("Cannot use deleted val. handle = "+a);return Tc[a].value},zc=a=>{switch(a){case void 0:return 1;case null:return 2;case !0:return 3;case !1:return 4;default:var b=Sc.length?Sc.pop():Tc.length;Tc[b]={hf:1,value:a};return b}};
function Wc(a,b,c){switch(b){case 0:return function(f){return this.fromWireType((c?lb:J)[f])};case 1:return function(f){return this.fromWireType((c?Za:Ya)[f>>1])};case 2:return function(f){return this.fromWireType((c?P:mb)[f>>2])};default:throw new TypeError("Unknown integer type: "+a);}}function Xc(a,b){var c=Lb[a];void 0===c&&X(b+" has unknown type "+Mc(a));return c}function xc(a){if(null===a)return"null";var b=typeof a;return"object"===b||"array"===b||"function"===b?a.toString():""+a}
function Yc(a,b){switch(b){case 2:return function(c){return this.fromWireType(U[c>>2])};case 3:return function(c){return this.fromWireType(nb[c>>3])};default:throw new TypeError("Unknown float type: "+a);}}
function Zc(a,b,c){switch(b){case 0:return c?function(f){return lb[f]}:function(f){return J[f]};case 1:return c?function(f){return Za[f>>1]}:function(f){return Ya[f>>1]};case 2:return c?function(f){return P[f>>2]}:function(f){return mb[f>>2]};default:throw new TypeError("Unknown integer type: "+a);}}var $c={};function ad(a){var b=$c[a];return void 0===b?cc(a):b}var bd=[];
function cd(){function a(b){b.$$$embind_global$$$=b;var c="object"==typeof $$$embind_global$$$&&b.$$$embind_global$$$==b;c||delete b.$$$embind_global$$$;return c}if("object"==typeof globalThis)return globalThis;if("object"==typeof $$$embind_global$$$)return $$$embind_global$$$;"object"==typeof global&&a(global)?$$$embind_global$$$=global:"object"==typeof self&&a(self)&&($$$embind_global$$$=self);if("object"==typeof $$$embind_global$$$)return $$$embind_global$$$;throw Error("unable to get global object.");
}function dd(a){var b=bd.length;bd.push(a);return b}function ed(a,b){for(var c=Array(a),f=0;f<a;++f)c[f]=Xc(P[(b>>2)+f],"parameter "+f);return c}var fd=[];function gd(a){var b=Array(a+1);return function(c,f,h){b[0]=c;for(var m=0;m<a;++m){var t=Xc(P[(f>>2)+m],"parameter "+m);b[m+1]=t.readValueFromPointer(h);h+=t.argPackAdvance}c=new (c.bind.apply(c,b));return zc(c)}}var hd={},jd;jd=za?()=>{var a=process.hrtime();return 1E3*a[0]+a[1]/1E6}:()=>performance.now();
function kd(a){var b=a.getExtension("ANGLE_instanced_arrays");b&&(a.vertexAttribDivisor=function(c,f){b.vertexAttribDivisorANGLE(c,f)},a.drawArraysInstanced=function(c,f,h,m){b.drawArraysInstancedANGLE(c,f,h,m)},a.drawElementsInstanced=function(c,f,h,m,t){b.drawElementsInstancedANGLE(c,f,h,m,t)})}
function ld(a){var b=a.getExtension("OES_vertex_array_object");b&&(a.createVertexArray=function(){return b.createVertexArrayOES()},a.deleteVertexArray=function(c){b.deleteVertexArrayOES(c)},a.bindVertexArray=function(c){b.bindVertexArrayOES(c)},a.isVertexArray=function(c){return b.isVertexArrayOES(c)})}function md(a){var b=a.getExtension("WEBGL_draw_buffers");b&&(a.drawBuffers=function(c,f){b.drawBuffersWEBGL(c,f)})}
var nd=1,od=[],pd=[],qd=[],rd=[],ka=[],sd=[],td=[],na=[],ud=[],vd=[],wd={},xd={},yd=4;function Bd(a){Cd||(Cd=a)}function ha(a){for(var b=nd++,c=a.length;c<b;c++)a[c]=null;return b}function la(a,b){a.lf||(a.lf=a.getContext,a.getContext=function(f,h){h=a.lf(f,h);return"webgl"==f==h instanceof WebGLRenderingContext?h:null});var c=1<b.majorVersion?a.getContext("webgl2",b):a.getContext("webgl",b);return c?Dd(c,b):0}
function Dd(a,b){var c=ha(na),f={Xf:c,attributes:b,version:b.majorVersion,ke:a};a.canvas&&(a.canvas.yf=f);na[c]=f;("undefined"==typeof b.Mf||b.Mf)&&Ed(f);return c}function ma(a){w=na[a];v.wg=Y=w&&w.ke;return!(a&&!Y)}
function Ed(a){a||(a=w);if(!a.Zf){a.Zf=!0;var b=a.ke;kd(b);ld(b);md(b);b.qf=b.getExtension("WEBGL_draw_instanced_base_vertex_base_instance");b.uf=b.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance");2<=a.version&&(b.rf=b.getExtension("EXT_disjoint_timer_query_webgl2"));if(2>a.version||!b.rf)b.rf=b.getExtension("EXT_disjoint_timer_query");b.yg=b.getExtension("WEBGL_multi_draw");(b.getSupportedExtensions()||[]).forEach(function(c){c.includes("lose_context")||c.includes("debug")||b.getExtension(c)})}}
var w,Cd,Fd=[];function Gd(a,b,c,f){for(var h=0;h<a;h++){var m=Y[c](),t=m&&ha(f);m?(m.name=t,f[t]=m):Bd(1282);P[b+4*h>>2]=t}}
function Hd(a,b,c){if(b){var f=void 0;switch(a){case 36346:f=1;break;case 36344:0!=c&&1!=c&&Bd(1280);return;case 34814:case 36345:f=0;break;case 34466:var h=Y.getParameter(34467);f=h?h.length:0;break;case 33309:if(2>w.version){Bd(1282);return}f=2*(Y.getSupportedExtensions()||[]).length;break;case 33307:case 33308:if(2>w.version){Bd(1280);return}f=33307==a?3:0}if(void 0===f)switch(h=Y.getParameter(a),typeof h){case "number":f=h;break;case "boolean":f=h?1:0;break;case "string":Bd(1280);return;case "object":if(null===
h)switch(a){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:f=0;break;default:Bd(1280);return}else{if(h instanceof Float32Array||h instanceof Uint32Array||h instanceof Int32Array||h instanceof Array){for(a=0;a<h.length;++a)switch(c){case 0:P[b+4*a>>2]=h[a];break;case 2:U[b+4*a>>2]=h[a];break;case 4:lb[b+a>>0]=h[a]?1:0}return}try{f=h.name|
0}catch(m){Bd(1280);Ka("GL_INVALID_ENUM in glGet"+c+"v: Unknown object returned from WebGL getParameter("+a+")! (error: "+m+")");return}}break;default:Bd(1280);Ka("GL_INVALID_ENUM in glGet"+c+"v: Native code calling glGet"+c+"v("+a+") and it returns "+h+" of type "+typeof h+"!");return}switch(c){case 1:c=f;mb[b>>2]=c;mb[b+4>>2]=(c-mb[b>>2])/4294967296;break;case 0:P[b>>2]=f;break;case 2:U[b>>2]=f;break;case 4:lb[b>>0]=f?1:0}}else Bd(1281)}
function Id(a){var b=oa(a)+1,c=Jd(b);ra(a,J,c,b);return c}function Kd(a){return"]"==a.slice(-1)&&a.lastIndexOf("[")}function Ld(a){a-=5120;return 0==a?lb:1==a?J:2==a?Za:4==a?P:6==a?U:5==a||28922==a||28520==a||30779==a||30782==a?mb:Ya}function Md(a,b,c,f,h){a=Ld(a);var m=31-Math.clz32(a.BYTES_PER_ELEMENT),t=yd;return a.subarray(h>>m,h+f*(c*({5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4}[b-6402]||1)*(1<<m)+t-1&-t)>>m)}
function Z(a){var b=Y.If;if(b){var c=b.Oe[a];"number"==typeof c&&(b.Oe[a]=c=Y.getUniformLocation(b,b.wf[a]+(0<c?"["+c+"]":"")));return c}Bd(1282)}var Nd=[],Od=[],Pd={};
function Qd(){if(!Rd){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:va||"./this.program"},b;for(b in Pd)void 0===Pd[b]?delete a[b]:a[b]=Pd[b];var c=[];for(b in a)c.push(b+"="+a[b]);Rd=c}return Rd}var Rd;function Sd(a){return 0===a%4&&(0!==a%100||0===a%400)}var Td=[31,29,31,30,31,30,31,31,30,31,30,31],Ud=[31,28,31,30,31,30,31,31,30,31,30,31];
function Vd(a,b,c,f){function h(A,N,S){for(A="number"==typeof A?A.toString():A||"";A.length<N;)A=S[0]+A;return A}function m(A,N){return h(A,N,"0")}function t(A,N){function S(pa){return 0>pa?-1:0<pa?1:0}var T;0===(T=S(A.getFullYear()-N.getFullYear()))&&0===(T=S(A.getMonth()-N.getMonth()))&&(T=S(A.getDate()-N.getDate()));return T}function n(A){switch(A.getDay()){case 0:return new Date(A.getFullYear()-1,11,29);case 1:return A;case 2:return new Date(A.getFullYear(),0,3);case 3:return new Date(A.getFullYear(),
0,2);case 4:return new Date(A.getFullYear(),0,1);case 5:return new Date(A.getFullYear()-1,11,31);case 6:return new Date(A.getFullYear()-1,11,30)}}function q(A){var N=A.qe;for(A=new Date((new Date(A.re+1900,0,1)).getTime());0<N;){var S=A.getMonth(),T=(Sd(A.getFullYear())?Td:Ud)[S];if(N>T-A.getDate())N-=T-A.getDate()+1,A.setDate(1),11>S?A.setMonth(S+1):(A.setMonth(0),A.setFullYear(A.getFullYear()+1));else{A.setDate(A.getDate()+N);break}}S=new Date(A.getFullYear()+1,0,4);N=n(new Date(A.getFullYear(),
0,4));S=n(S);return 0>=t(N,A)?0>=t(S,A)?A.getFullYear()+1:A.getFullYear():A.getFullYear()-1}var x=P[f+40>>2];f={sg:P[f>>2],rg:P[f+4>>2],Ye:P[f+8>>2],jf:P[f+12>>2],Ze:P[f+16>>2],re:P[f+20>>2],je:P[f+24>>2],qe:P[f+28>>2],Bg:P[f+32>>2],qg:P[f+36>>2],tg:x?Va(x):""};c=Va(c);x={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y",
"%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var C in x)c=c.replace(new RegExp(C,"g"),x[C]);var H="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),M="January February March April May June July August September October November December".split(" ");x={"%a":function(A){return H[A.je].substring(0,3)},"%A":function(A){return H[A.je]},"%b":function(A){return M[A.Ze].substring(0,3)},
"%B":function(A){return M[A.Ze]},"%C":function(A){return m((A.re+1900)/100|0,2)},"%d":function(A){return m(A.jf,2)},"%e":function(A){return h(A.jf,2," ")},"%g":function(A){return q(A).toString().substring(2)},"%G":function(A){return q(A)},"%H":function(A){return m(A.Ye,2)},"%I":function(A){A=A.Ye;0==A?A=12:12<A&&(A-=12);return m(A,2)},"%j":function(A){for(var N=0,S=0;S<=A.Ze-1;N+=(Sd(A.re+1900)?Td:Ud)[S++]);return m(A.jf+N,3)},"%m":function(A){return m(A.Ze+1,2)},"%M":function(A){return m(A.rg,2)},
"%n":function(){return"\n"},"%p":function(A){return 0<=A.Ye&&12>A.Ye?"AM":"PM"},"%S":function(A){return m(A.sg,2)},"%t":function(){return"\t"},"%u":function(A){return A.je||7},"%U":function(A){return m(Math.floor((A.qe+7-A.je)/7),2)},"%V":function(A){var N=Math.floor((A.qe+7-(A.je+6)%7)/7);2>=(A.je+371-A.qe-2)%7&&N++;if(N)53==N&&(S=(A.je+371-A.qe)%7,4==S||3==S&&Sd(A.re)||(N=1));else{N=52;var S=(A.je+7-A.qe-1)%7;(4==S||5==S&&Sd(A.re%400-1))&&N++}return m(N,2)},"%w":function(A){return A.je},"%W":function(A){return m(Math.floor((A.qe+
7-(A.je+6)%7)/7),2)},"%y":function(A){return(A.re+1900).toString().substring(2)},"%Y":function(A){return A.re+1900},"%z":function(A){A=A.qg;var N=0<=A;A=Math.abs(A)/60;return(N?"+":"-")+String("0000"+(A/60*100+A%60)).slice(-4)},"%Z":function(A){return A.tg},"%%":function(){return"%"}};c=c.replace(/%%/g,"\x00\x00");for(C in x)c.includes(C)&&(c=c.replace(new RegExp(C,"g"),x[C](f)));c=c.replace(/\0\0/g,"%");C=Wd(c);if(C.length>b)return 0;lb.set(C,a);return C.length-1}Qb=v.InternalError=Pb("InternalError");
for(var Xd=Array(256),Yd=0;256>Yd;++Yd)Xd[Yd]=String.fromCharCode(Yd);bc=Xd;dc=v.BindingError=Pb("BindingError");rc.prototype.isAliasOf=function(a){if(!(this instanceof rc&&a instanceof rc))return!1;var b=this.Md.Yd.Sd,c=this.Md.Ud,f=a.Md.Yd.Sd;for(a=a.Md.Ud;b.ie;)c=b.Pe(c),b=b.ie;for(;f.ie;)a=f.Pe(a),f=f.ie;return b===f&&c===a};
rc.prototype.clone=function(){this.Md.Ud||ec(this);if(this.Md.Ne)return this.Md.count.value+=1,this;var a=qc,b=Object,c=b.create,f=Object.getPrototypeOf(this),h=this.Md;a=a(c.call(b,f,{Md:{value:{count:h.count,Ge:h.Ge,Ne:h.Ne,Ud:h.Ud,Yd:h.Yd,ae:h.ae,ge:h.ge}}}));a.Md.count.value+=1;a.Md.Ge=!1;return a};rc.prototype["delete"]=function(){this.Md.Ud||ec(this);this.Md.Ge&&!this.Md.Ne&&X("Object already scheduled for deletion");gc(this);hc(this.Md);this.Md.Ne||(this.Md.ae=void 0,this.Md.Ud=void 0)};
rc.prototype.isDeleted=function(){return!this.Md.Ud};rc.prototype.deleteLater=function(){this.Md.Ud||ec(this);this.Md.Ge&&!this.Md.Ne&&X("Object already scheduled for deletion");kc.push(this);1===kc.length&&mc&&mc(lc);this.Md.Ge=!0;return this};v.getInheritedInstanceCount=function(){return Object.keys(nc).length};v.getLiveInheritedInstances=function(){var a=[],b;for(b in nc)nc.hasOwnProperty(b)&&a.push(nc[b]);return a};v.flushPendingDeletes=lc;v.setDelayFunction=function(a){mc=a;kc.length&&mc&&mc(lc)};
Bc.prototype.Sf=function(a){this.vf&&(a=this.vf(a));return a};Bc.prototype.pf=function(a){this.me&&this.me(a)};Bc.prototype.argPackAdvance=8;Bc.prototype.readValueFromPointer=Jb;Bc.prototype.deleteObject=function(a){if(null!==a)a["delete"]()};
Bc.prototype.fromWireType=function(a){function b(){return this.Ue?pc(this.Sd.He,{Yd:this.dg,Ud:c,ge:this,ae:a}):pc(this.Sd.He,{Yd:this,Ud:a})}var c=this.Sf(a);if(!c)return this.pf(a),null;var f=oc(this.Sd,c);if(void 0!==f){if(0===f.Md.count.value)return f.Md.Ud=c,f.Md.ae=a,f.clone();f=f.clone();this.pf(a);return f}f=this.Sd.Rf(c);f=jc[f];if(!f)return b.call(this);f=this.Te?f.Gf:f.pointerType;var h=ic(c,this.Sd,f.Sd);return null===h?b.call(this):this.Ue?pc(f.Sd.He,{Yd:f,Ud:h,ge:this,ae:a}):pc(f.Sd.He,
{Yd:f,Ud:h})};Lc=v.UnboundTypeError=Pb("UnboundTypeError");v.count_emval_handles=function(){for(var a=0,b=5;b<Tc.length;++b)void 0!==Tc[b]&&++a;return a};v.get_first_emval=function(){for(var a=5;a<Tc.length;++a)if(void 0!==Tc[a])return Tc[a];return null};for(var Y,Zd=0;32>Zd;++Zd)Fd.push(Array(Zd));var $d=new Float32Array(288);for(Zd=0;288>Zd;++Zd)Nd[Zd]=$d.subarray(0,Zd+1);var ae=new Int32Array(288);for(Zd=0;288>Zd;++Zd)Od[Zd]=ae.subarray(0,Zd+1);
function Wd(a){var b=Array(oa(a)+1);ra(a,b,0,b.length);return b}
var qe={V:function(){return 0},Bb:function(){},Db:function(){return 0},yb:function(){},zb:function(){},W:function(){},Ab:function(){},E:function(a){var b=Hb[a];delete Hb[a];var c=b.gf,f=b.me,h=b.sf,m=h.map(t=>t.Wf).concat(h.map(t=>t.ig));Sb([a],m,t=>{var n={};h.forEach((q,x)=>{var C=t[x],H=q.Uf,M=q.Vf,A=t[x+h.length],N=q.hg,S=q.jg;n[q.Nf]={read:T=>C.fromWireType(H(M,T)),write:(T,pa)=>{var ta=[];N(S,T,A.toWireType(ta,pa));Ib(ta)}}});return[{name:b.name,fromWireType:function(q){var x={},C;for(C in n)x[C]=
n[C].read(q);f(q);return x},toWireType:function(q,x){for(var C in n)if(!(C in x))throw new TypeError('Missing field:  "'+C+'"');var H=c();for(C in n)n[C].write(H,x[C]);null!==q&&q.push(f,H);return H},argPackAdvance:8,readValueFromPointer:Jb,fe:f}]})},qb:function(){},Hb:function(a,b,c,f,h){var m=Ub(c);b=cc(b);Tb(a,{name:b,fromWireType:function(t){return!!t},toWireType:function(t,n){return n?f:h},argPackAdvance:8,readValueFromPointer:function(t){if(1===c)var n=lb;else if(2===c)n=Za;else if(4===c)n=
P;else throw new TypeError("Unknown boolean type size: "+b);return this.fromWireType(n[t>>m])},fe:null})},p:function(a,b,c,f,h,m,t,n,q,x,C,H,M){C=cc(C);m=Kc(h,m);n&&(n=Kc(t,n));x&&(x=Kc(q,x));M=Kc(H,M);var A=Nb(C);tc(A,function(){Pc("Cannot construct "+C+" due to unbound types",[f])});Sb([a,b,c],f?[f]:[],function(N){N=N[0];if(f){var S=N.Sd;var T=S.He}else T=rc.prototype;N=Ob(A,function(){if(Object.getPrototypeOf(this)!==pa)throw new dc("Use 'new' to construct "+C);if(void 0===ta.pe)throw new dc(C+
" has no accessible constructor");var hb=ta.pe[arguments.length];if(void 0===hb)throw new dc("Tried to invoke ctor of "+C+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(ta.pe).toString()+") parameters instead!");return hb.apply(this,arguments)});var pa=Object.create(T,{constructor:{value:N}});N.prototype=pa;var ta=new uc(C,N,pa,M,S,m,n,x);S=new Bc(C,ta,!0,!1,!1);T=new Bc(C+"*",ta,!1,!1,!1);var gb=new Bc(C+" const*",ta,!1,!0,!1);jc[a]={pointerType:T,Gf:gb};Ic(A,
N);return[S,T,gb]})},h:function(a,b,c,f,h,m,t){var n=Rc(c,f);b=cc(b);m=Kc(h,m);Sb([],[a],function(q){function x(){Pc("Cannot call "+C+" due to unbound types",n)}q=q[0];var C=q.name+"."+b;b.startsWith("@@")&&(b=Symbol[b.substring(2)]);var H=q.Sd.constructor;void 0===H[b]?(x.Ee=c-1,H[b]=x):(sc(H,b,C),H[b].$d[c-1]=x);Sb([],n,function(M){M=[M[0],null].concat(M.slice(1));M=Qc(C,M,null,m,t);void 0===H[b].$d?(M.Ee=c-1,H[b]=M):H[b].$d[c-1]=M;return[]});return[]})},A:function(a,b,c,f,h,m){0<b||Pa();var t=
Rc(b,c);h=Kc(f,h);Sb([],[a],function(n){n=n[0];var q="constructor "+n.name;void 0===n.Sd.pe&&(n.Sd.pe=[]);if(void 0!==n.Sd.pe[b-1])throw new dc("Cannot register multiple constructors with identical number of parameters ("+(b-1)+") for class '"+n.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");n.Sd.pe[b-1]=()=>{Pc("Cannot construct "+n.name+" due to unbound types",t)};Sb([],t,function(x){x.splice(1,0,null);n.Sd.pe[b-1]=Qc(q,x,null,h,m);return[]});
return[]})},b:function(a,b,c,f,h,m,t,n){var q=Rc(c,f);b=cc(b);m=Kc(h,m);Sb([],[a],function(x){function C(){Pc("Cannot call "+H+" due to unbound types",q)}x=x[0];var H=x.name+"."+b;b.startsWith("@@")&&(b=Symbol[b.substring(2)]);n&&x.Sd.eg.push(b);var M=x.Sd.He,A=M[b];void 0===A||void 0===A.$d&&A.className!==x.name&&A.Ee===c-2?(C.Ee=c-2,C.className=x.name,M[b]=C):(sc(M,b,H),M[b].$d[c-2]=C);Sb([],q,function(N){N=Qc(H,N,x,m,t);void 0===M[b].$d?(N.Ee=c-2,M[b]=N):M[b].$d[c-2]=N;return[]});return[]})},w:function(a,
b,c){a=cc(a);Sb([],[b],function(f){f=f[0];v[a]=f.fromWireType(c);return[]})},Gb:function(a,b){b=cc(b);Tb(a,{name:b,fromWireType:function(c){var f=Vc(c);Uc(c);return f},toWireType:function(c,f){return zc(f)},argPackAdvance:8,readValueFromPointer:Jb,fe:null})},o:function(a,b,c,f){function h(){}c=Ub(c);b=cc(b);h.values={};Tb(a,{name:b,constructor:h,fromWireType:function(m){return this.constructor.values[m]},toWireType:function(m,t){return t.value},argPackAdvance:8,readValueFromPointer:Wc(b,c,f),fe:null});
tc(b,h)},e:function(a,b,c){var f=Xc(a,"enum");b=cc(b);a=f.constructor;f=Object.create(f.constructor.prototype,{value:{value:c},constructor:{value:Ob(f.name+"_"+b,function(){})}});a.values[c]=f;a[b]=f},Z:function(a,b,c){c=Ub(c);b=cc(b);Tb(a,{name:b,fromWireType:function(f){return f},toWireType:function(f,h){return h},argPackAdvance:8,readValueFromPointer:Yc(b,c),fe:null})},y:function(a,b,c,f,h,m){var t=Rc(b,c);a=cc(a);h=Kc(f,h);tc(a,function(){Pc("Cannot call "+a+" due to unbound types",t)},b-1);Sb([],
t,function(n){n=[n[0],null].concat(n.slice(1));Ic(a,Qc(a,n,null,h,m),b-1);return[]})},B:function(a,b,c,f,h){b=cc(b);-1===h&&(h=4294967295);h=Ub(c);var m=n=>n;if(0===f){var t=32-8*c;m=n=>n<<t>>>t}c=b.includes("unsigned")?function(n,q){return q>>>0}:function(n,q){return q};Tb(a,{name:b,fromWireType:m,toWireType:c,argPackAdvance:8,readValueFromPointer:Zc(b,h,0!==f),fe:null})},t:function(a,b,c){function f(m){m>>=2;var t=mb;return new h(kb,t[m+1],t[m])}var h=[Int8Array,Uint8Array,Int16Array,Uint16Array,
Int32Array,Uint32Array,Float32Array,Float64Array][b];c=cc(c);Tb(a,{name:c,fromWireType:f,argPackAdvance:8,readValueFromPointer:f},{Yf:!0})},u:function(a,b,c,f,h,m,t,n,q,x,C,H){c=cc(c);m=Kc(h,m);n=Kc(t,n);x=Kc(q,x);H=Kc(C,H);Sb([a],[b],function(M){M=M[0];return[new Bc(c,M.Sd,!1,!1,!0,M,f,m,n,x,H)]})},Y:function(a,b){b=cc(b);var c="std::string"===b;Tb(a,{name:b,fromWireType:function(f){var h=mb[f>>2];if(c)for(var m=f+4,t=0;t<=h;++t){var n=f+4+t;if(t==h||0==J[n]){m=Va(m,n-m);if(void 0===q)var q=m;else q+=
String.fromCharCode(0),q+=m;m=n+1}}else{q=Array(h);for(t=0;t<h;++t)q[t]=String.fromCharCode(J[f+4+t]);q=q.join("")}Oc(f);return q},toWireType:function(f,h){h instanceof ArrayBuffer&&(h=new Uint8Array(h));var m="string"==typeof h;m||h instanceof Uint8Array||h instanceof Uint8ClampedArray||h instanceof Int8Array||X("Cannot pass non-string to std::string");var t=(c&&m?()=>oa(h):()=>h.length)(),n=Jd(4+t+1);mb[n>>2]=t;if(c&&m)ra(h,J,n+4,t+1);else if(m)for(m=0;m<t;++m){var q=h.charCodeAt(m);255<q&&(Oc(n),
X("String has UTF-16 code units that do not fit in 8 bits"));J[n+4+m]=q}else for(m=0;m<t;++m)J[n+4+m]=h[m];null!==f&&f.push(Oc,n);return n},argPackAdvance:8,readValueFromPointer:Jb,fe:function(f){Oc(f)}})},Q:function(a,b,c){c=cc(c);if(2===b){var f=Xa;var h=bb;var m=cb;var t=()=>Ya;var n=1}else 4===b&&(f=db,h=eb,m=jb,t=()=>mb,n=2);Tb(a,{name:c,fromWireType:function(q){for(var x=mb[q>>2],C=t(),H,M=q+4,A=0;A<=x;++A){var N=q+4+A*b;if(A==x||0==C[N>>n])M=f(M,N-M),void 0===H?H=M:(H+=String.fromCharCode(0),
H+=M),M=N+b}Oc(q);return H},toWireType:function(q,x){"string"!=typeof x&&X("Cannot pass non-string to C++ string type "+c);var C=m(x),H=Jd(4+C+b);mb[H>>2]=C>>n;h(x,H+4,C+b);null!==q&&q.push(Oc,H);return H},argPackAdvance:8,readValueFromPointer:Jb,fe:function(q){Oc(q)}})},F:function(a,b,c,f,h,m){Hb[a]={name:cc(b),gf:Kc(c,f),me:Kc(h,m),sf:[]}},g:function(a,b,c,f,h,m,t,n,q,x){Hb[a].sf.push({Nf:cc(b),Wf:c,Uf:Kc(f,h),Vf:m,ig:t,hg:Kc(n,q),jg:x})},Ib:function(a,b){b=cc(b);Tb(a,{$f:!0,name:b,argPackAdvance:0,
fromWireType:function(){},toWireType:function(){}})},Fb:function(){return!0},sb:function(){throw Infinity;},I:function(a,b,c){a=Vc(a);b=Xc(b,"emval::as");var f=[],h=zc(f);P[c>>2]=h;return b.toWireType(f,a)},K:function(a,b,c,f,h){a=bd[a];b=Vc(b);c=ad(c);var m=[];P[f>>2]=zc(m);return a(b,c,m,h)},D:function(a,b,c,f){a=bd[a];b=Vc(b);c=ad(c);a(b,c,null,f)},f:Uc,M:function(a){if(0===a)return zc(cd());a=ad(a);return zc(cd()[a])},z:function(a,b){var c=ed(a,b),f=c[0];b=f.name+"_$"+c.slice(1).map(function(t){return t.name}).join("_")+
"$";var h=fd[b];if(void 0!==h)return h;var m=Array(a-1);h=dd((t,n,q,x)=>{for(var C=0,H=0;H<a-1;++H)m[H]=c[H+1].readValueFromPointer(x+C),C+=c[H+1].argPackAdvance;t=t[n].apply(t,m);for(H=0;H<a-1;++H)c[H+1].Jf&&c[H+1].Jf(m[H]);if(!f.$f)return f.toWireType(q,t)});return fd[b]=h},H:function(a,b){a=Vc(a);b=Vc(b);return zc(a[b])},r:function(a){4<a&&(Tc[a].hf+=1)},L:function(a,b,c,f){a=Vc(a);var h=hd[b];h||(h=gd(b),hd[b]=h);return h(a,c,f)},O:function(){return zc([])},j:function(a){return zc(ad(a))},G:function(){return zc({})},
mb:function(a){a=Vc(a);return!a},C:function(a){var b=Vc(a);Ib(b);Uc(a)},l:function(a,b,c){a=Vc(a);b=Vc(b);c=Vc(c);a[b]=c},i:function(a,b){a=Xc(a,"_emval_take_value");a=a.readValueFromPointer(b);return zc(a)},ub:function(){return-52},vb:function(){},a:function(){Pa("")},Eb:jd,bd:function(a){Y.activeTexture(a)},cd:function(a,b){Y.attachShader(pd[a],sd[b])},ca:function(a,b,c){Y.bindAttribLocation(pd[a],b,Va(c))},da:function(a,b){35051==a?Y.ef=b:35052==a&&(Y.Fe=b);Y.bindBuffer(a,od[b])},ba:function(a,
b){Y.bindFramebuffer(a,qd[b])},fc:function(a,b){Y.bindRenderbuffer(a,rd[b])},Rb:function(a,b){Y.bindSampler(a,ud[b])},ea:function(a,b){Y.bindTexture(a,ka[b])},zc:function(a){Y.bindVertexArray(td[a])},Dc:function(a){Y.bindVertexArray(td[a])},fa:function(a,b,c,f){Y.blendColor(a,b,c,f)},ga:function(a){Y.blendEquation(a)},ha:function(a,b){Y.blendFunc(a,b)},$b:function(a,b,c,f,h,m,t,n,q,x){Y.blitFramebuffer(a,b,c,f,h,m,t,n,q,x)},ia:function(a,b,c,f){2<=w.version?c?Y.bufferData(a,J,f,c,b):Y.bufferData(a,
b,f):Y.bufferData(a,c?J.subarray(c,c+b):b,f)},ja:function(a,b,c,f){2<=w.version?Y.bufferSubData(a,b,J,f,c):Y.bufferSubData(a,b,J.subarray(f,f+c))},gc:function(a){return Y.checkFramebufferStatus(a)},T:function(a){Y.clear(a)},aa:function(a,b,c,f){Y.clearColor(a,b,c,f)},X:function(a){Y.clearStencil(a)},kb:function(a,b,c,f){return Y.clientWaitSync(vd[a],b,(c>>>0)+4294967296*f)},ka:function(a,b,c,f){Y.colorMask(!!a,!!b,!!c,!!f)},la:function(a){Y.compileShader(sd[a])},ma:function(a,b,c,f,h,m,t,n){2<=w.version?
Y.Fe?Y.compressedTexImage2D(a,b,c,f,h,m,t,n):Y.compressedTexImage2D(a,b,c,f,h,m,J,n,t):Y.compressedTexImage2D(a,b,c,f,h,m,n?J.subarray(n,n+t):null)},na:function(a,b,c,f,h,m,t,n,q){2<=w.version?Y.Fe?Y.compressedTexSubImage2D(a,b,c,f,h,m,t,n,q):Y.compressedTexSubImage2D(a,b,c,f,h,m,t,J,q,n):Y.compressedTexSubImage2D(a,b,c,f,h,m,t,q?J.subarray(q,q+n):null)},Zb:function(a,b,c,f,h){Y.copyBufferSubData(a,b,c,f,h)},oa:function(a,b,c,f,h,m,t,n){Y.copyTexSubImage2D(a,b,c,f,h,m,t,n)},pa:function(){var a=ha(pd),
b=Y.createProgram();b.name=a;b.Xe=b.Ve=b.We=0;b.kf=1;pd[a]=b;return a},qa:function(a){var b=ha(sd);sd[b]=Y.createShader(a);return b},ra:function(a){Y.cullFace(a)},sa:function(a,b){for(var c=0;c<a;c++){var f=P[b+4*c>>2],h=od[f];h&&(Y.deleteBuffer(h),h.name=0,od[f]=null,f==Y.ef&&(Y.ef=0),f==Y.Fe&&(Y.Fe=0))}},hc:function(a,b){for(var c=0;c<a;++c){var f=P[b+4*c>>2],h=qd[f];h&&(Y.deleteFramebuffer(h),h.name=0,qd[f]=null)}},ta:function(a){if(a){var b=pd[a];b?(Y.deleteProgram(b),b.name=0,pd[a]=null):Bd(1281)}},
ic:function(a,b){for(var c=0;c<a;c++){var f=P[b+4*c>>2],h=rd[f];h&&(Y.deleteRenderbuffer(h),h.name=0,rd[f]=null)}},Sb:function(a,b){for(var c=0;c<a;c++){var f=P[b+4*c>>2],h=ud[f];h&&(Y.deleteSampler(h),h.name=0,ud[f]=null)}},ua:function(a){if(a){var b=sd[a];b?(Y.deleteShader(b),sd[a]=null):Bd(1281)}},_b:function(a){if(a){var b=vd[a];b?(Y.deleteSync(b),b.name=0,vd[a]=null):Bd(1281)}},va:function(a,b){for(var c=0;c<a;c++){var f=P[b+4*c>>2],h=ka[f];h&&(Y.deleteTexture(h),h.name=0,ka[f]=null)}},Ac:function(a,
b){for(var c=0;c<a;c++){var f=P[b+4*c>>2];Y.deleteVertexArray(td[f]);td[f]=null}},Ec:function(a,b){for(var c=0;c<a;c++){var f=P[b+4*c>>2];Y.deleteVertexArray(td[f]);td[f]=null}},wa:function(a){Y.depthMask(!!a)},xa:function(a){Y.disable(a)},ya:function(a){Y.disableVertexAttribArray(a)},za:function(a,b,c){Y.drawArrays(a,b,c)},yc:function(a,b,c,f){Y.drawArraysInstanced(a,b,c,f)},vc:function(a,b,c,f,h){Y.qf.drawArraysInstancedBaseInstanceWEBGL(a,b,c,f,h)},tc:function(a,b){for(var c=Fd[a],f=0;f<a;f++)c[f]=
P[b+4*f>>2];Y.drawBuffers(c)},Aa:function(a,b,c,f){Y.drawElements(a,b,c,f)},xc:function(a,b,c,f,h){Y.drawElementsInstanced(a,b,c,f,h)},wc:function(a,b,c,f,h,m,t){Y.qf.drawElementsInstancedBaseVertexBaseInstanceWEBGL(a,b,c,f,h,m,t)},nc:function(a,b,c,f,h,m){Y.drawElements(a,f,h,m)},Ba:function(a){Y.enable(a)},Ca:function(a){Y.enableVertexAttribArray(a)},Xb:function(a,b){return(a=Y.fenceSync(a,b))?(b=ha(vd),a.name=b,vd[b]=a,b):0},Da:function(){Y.finish()},Ea:function(){Y.flush()},jc:function(a,b,c,
f){Y.framebufferRenderbuffer(a,b,c,rd[f])},kc:function(a,b,c,f,h){Y.framebufferTexture2D(a,b,c,ka[f],h)},Fa:function(a){Y.frontFace(a)},Ga:function(a,b){Gd(a,b,"createBuffer",od)},lc:function(a,b){Gd(a,b,"createFramebuffer",qd)},mc:function(a,b){Gd(a,b,"createRenderbuffer",rd)},Tb:function(a,b){Gd(a,b,"createSampler",ud)},Ha:function(a,b){Gd(a,b,"createTexture",ka)},Bc:function(a,b){Gd(a,b,"createVertexArray",td)},Cc:function(a,b){Gd(a,b,"createVertexArray",td)},bc:function(a){Y.generateMipmap(a)},
Ia:function(a,b,c){c?P[c>>2]=Y.getBufferParameter(a,b):Bd(1281)},Ja:function(){var a=Y.getError()||Cd;Cd=0;return a},Ka:function(a,b){Hd(a,b,2)},cc:function(a,b,c,f){a=Y.getFramebufferAttachmentParameter(a,b,c);if(a instanceof WebGLRenderbuffer||a instanceof WebGLTexture)a=a.name|0;P[f>>2]=a},N:function(a,b){Hd(a,b,0)},La:function(a,b,c,f){a=Y.getProgramInfoLog(pd[a]);null===a&&(a="(unknown error)");b=0<b&&f?ra(a,J,f,b):0;c&&(P[c>>2]=b)},Ma:function(a,b,c){if(c)if(a>=nd)Bd(1281);else if(a=pd[a],35716==
b)a=Y.getProgramInfoLog(a),null===a&&(a="(unknown error)"),P[c>>2]=a.length+1;else if(35719==b){if(!a.Xe)for(b=0;b<Y.getProgramParameter(a,35718);++b)a.Xe=Math.max(a.Xe,Y.getActiveUniform(a,b).name.length+1);P[c>>2]=a.Xe}else if(35722==b){if(!a.Ve)for(b=0;b<Y.getProgramParameter(a,35721);++b)a.Ve=Math.max(a.Ve,Y.getActiveAttrib(a,b).name.length+1);P[c>>2]=a.Ve}else if(35381==b){if(!a.We)for(b=0;b<Y.getProgramParameter(a,35382);++b)a.We=Math.max(a.We,Y.getActiveUniformBlockName(a,b).length+1);P[c>>
2]=a.We}else P[c>>2]=Y.getProgramParameter(a,b);else Bd(1281)},dc:function(a,b,c){c?P[c>>2]=Y.getRenderbufferParameter(a,b):Bd(1281)},Na:function(a,b,c,f){a=Y.getShaderInfoLog(sd[a]);null===a&&(a="(unknown error)");b=0<b&&f?ra(a,J,f,b):0;c&&(P[c>>2]=b)},Ob:function(a,b,c,f){a=Y.getShaderPrecisionFormat(a,b);P[c>>2]=a.rangeMin;P[c+4>>2]=a.rangeMax;P[f>>2]=a.precision},Oa:function(a,b,c){c?35716==b?(a=Y.getShaderInfoLog(sd[a]),null===a&&(a="(unknown error)"),P[c>>2]=a?a.length+1:0):35720==b?(a=Y.getShaderSource(sd[a]),
P[c>>2]=a?a.length+1:0):P[c>>2]=Y.getShaderParameter(sd[a],b):Bd(1281)},S:function(a){var b=wd[a];if(!b){switch(a){case 7939:b=Y.getSupportedExtensions()||[];b=b.concat(b.map(function(f){return"GL_"+f}));b=Id(b.join(" "));break;case 7936:case 7937:case 37445:case 37446:(b=Y.getParameter(a))||Bd(1280);b=b&&Id(b);break;case 7938:b=Y.getParameter(7938);b=2<=w.version?"OpenGL ES 3.0 ("+b+")":"OpenGL ES 2.0 ("+b+")";b=Id(b);break;case 35724:b=Y.getParameter(35724);var c=b.match(/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/);
null!==c&&(3==c[1].length&&(c[1]+="0"),b="OpenGL ES GLSL ES "+c[1]+" ("+b+")");b=Id(b);break;default:Bd(1280)}wd[a]=b}return b},jb:function(a,b){if(2>w.version)return Bd(1282),0;var c=xd[a];if(c)return 0>b||b>=c.length?(Bd(1281),0):c[b];switch(a){case 7939:return c=Y.getSupportedExtensions()||[],c=c.concat(c.map(function(f){return"GL_"+f})),c=c.map(function(f){return Id(f)}),c=xd[a]=c,0>b||b>=c.length?(Bd(1281),0):c[b];default:return Bd(1280),0}},Pa:function(a,b){b=Va(b);if(a=pd[a]){var c=a,f=c.Oe,
h=c.xf,m;if(!f)for(c.Oe=f={},c.wf={},m=0;m<Y.getProgramParameter(c,35718);++m){var t=Y.getActiveUniform(c,m);var n=t.name;t=t.size;var q=Kd(n);q=0<q?n.slice(0,q):n;var x=c.kf;c.kf+=t;h[q]=[t,x];for(n=0;n<t;++n)f[x]=n,c.wf[x++]=q}c=a.Oe;f=0;h=b;m=Kd(b);0<m&&(f=parseInt(b.slice(m+1))>>>0,h=b.slice(0,m));if((h=a.xf[h])&&f<h[0]&&(f+=h[1],c[f]=c[f]||Y.getUniformLocation(a,b)))return f}else Bd(1281);return-1},Pb:function(a,b,c){for(var f=Fd[b],h=0;h<b;h++)f[h]=P[c+4*h>>2];Y.invalidateFramebuffer(a,f)},
Qb:function(a,b,c,f,h,m,t){for(var n=Fd[b],q=0;q<b;q++)n[q]=P[c+4*q>>2];Y.invalidateSubFramebuffer(a,n,f,h,m,t)},Yb:function(a){return Y.isSync(vd[a])},Qa:function(a){return(a=ka[a])?Y.isTexture(a):0},Ra:function(a){Y.lineWidth(a)},Sa:function(a){a=pd[a];Y.linkProgram(a);a.Oe=0;a.xf={}},rc:function(a,b,c,f,h,m){Y.uf.multiDrawArraysInstancedBaseInstanceWEBGL(a,P,b>>2,P,c>>2,P,f>>2,mb,h>>2,m)},sc:function(a,b,c,f,h,m,t,n){Y.uf.multiDrawElementsInstancedBaseVertexBaseInstanceWEBGL(a,P,b>>2,c,P,f>>2,
P,h>>2,P,m>>2,mb,t>>2,n)},Ta:function(a,b){3317==a&&(yd=b);Y.pixelStorei(a,b)},uc:function(a){Y.readBuffer(a)},Ua:function(a,b,c,f,h,m,t){if(2<=w.version)if(Y.ef)Y.readPixels(a,b,c,f,h,m,t);else{var n=Ld(m);Y.readPixels(a,b,c,f,h,m,n,t>>31-Math.clz32(n.BYTES_PER_ELEMENT))}else(t=Md(m,h,c,f,t))?Y.readPixels(a,b,c,f,h,m,t):Bd(1280)},ec:function(a,b,c,f){Y.renderbufferStorage(a,b,c,f)},ac:function(a,b,c,f,h){Y.renderbufferStorageMultisample(a,b,c,f,h)},Ub:function(a,b,c){Y.samplerParameterf(ud[a],b,
c)},Vb:function(a,b,c){Y.samplerParameteri(ud[a],b,c)},Wb:function(a,b,c){Y.samplerParameteri(ud[a],b,P[c>>2])},Va:function(a,b,c,f){Y.scissor(a,b,c,f)},Wa:function(a,b,c,f){for(var h="",m=0;m<b;++m){var t=f?P[f+4*m>>2]:-1;h+=Va(P[c+4*m>>2],0>t?void 0:t)}Y.shaderSource(sd[a],h)},Xa:function(a,b,c){Y.stencilFunc(a,b,c)},Ya:function(a,b,c,f){Y.stencilFuncSeparate(a,b,c,f)},Za:function(a){Y.stencilMask(a)},_a:function(a,b){Y.stencilMaskSeparate(a,b)},$a:function(a,b,c){Y.stencilOp(a,b,c)},ab:function(a,
b,c,f){Y.stencilOpSeparate(a,b,c,f)},bb:function(a,b,c,f,h,m,t,n,q){if(2<=w.version)if(Y.Fe)Y.texImage2D(a,b,c,f,h,m,t,n,q);else if(q){var x=Ld(n);Y.texImage2D(a,b,c,f,h,m,t,n,x,q>>31-Math.clz32(x.BYTES_PER_ELEMENT))}else Y.texImage2D(a,b,c,f,h,m,t,n,null);else Y.texImage2D(a,b,c,f,h,m,t,n,q?Md(n,t,f,h,q):null)},cb:function(a,b,c){Y.texParameterf(a,b,c)},db:function(a,b,c){Y.texParameterf(a,b,U[c>>2])},eb:function(a,b,c){Y.texParameteri(a,b,c)},fb:function(a,b,c){Y.texParameteri(a,b,P[c>>2])},oc:function(a,
b,c,f,h){Y.texStorage2D(a,b,c,f,h)},gb:function(a,b,c,f,h,m,t,n,q){if(2<=w.version)if(Y.Fe)Y.texSubImage2D(a,b,c,f,h,m,t,n,q);else if(q){var x=Ld(n);Y.texSubImage2D(a,b,c,f,h,m,t,n,x,q>>31-Math.clz32(x.BYTES_PER_ELEMENT))}else Y.texSubImage2D(a,b,c,f,h,m,t,n,null);else x=null,q&&(x=Md(n,t,h,m,q)),Y.texSubImage2D(a,b,c,f,h,m,t,n,x)},hb:function(a,b){Y.uniform1f(Z(a),b)},ib:function(a,b,c){if(2<=w.version)Y.uniform1fv(Z(a),U,c>>2,b);else{if(288>=b)for(var f=Nd[b-1],h=0;h<b;++h)f[h]=U[c+4*h>>2];else f=
U.subarray(c>>2,c+4*b>>2);Y.uniform1fv(Z(a),f)}},Zc:function(a,b){Y.uniform1i(Z(a),b)},_c:function(a,b,c){if(2<=w.version)Y.uniform1iv(Z(a),P,c>>2,b);else{if(288>=b)for(var f=Od[b-1],h=0;h<b;++h)f[h]=P[c+4*h>>2];else f=P.subarray(c>>2,c+4*b>>2);Y.uniform1iv(Z(a),f)}},$c:function(a,b,c){Y.uniform2f(Z(a),b,c)},ad:function(a,b,c){if(2<=w.version)Y.uniform2fv(Z(a),U,c>>2,2*b);else{if(144>=b)for(var f=Nd[2*b-1],h=0;h<2*b;h+=2)f[h]=U[c+4*h>>2],f[h+1]=U[c+(4*h+4)>>2];else f=U.subarray(c>>2,c+8*b>>2);Y.uniform2fv(Z(a),
f)}},Yc:function(a,b,c){Y.uniform2i(Z(a),b,c)},Xc:function(a,b,c){if(2<=w.version)Y.uniform2iv(Z(a),P,c>>2,2*b);else{if(144>=b)for(var f=Od[2*b-1],h=0;h<2*b;h+=2)f[h]=P[c+4*h>>2],f[h+1]=P[c+(4*h+4)>>2];else f=P.subarray(c>>2,c+8*b>>2);Y.uniform2iv(Z(a),f)}},Wc:function(a,b,c,f){Y.uniform3f(Z(a),b,c,f)},Vc:function(a,b,c){if(2<=w.version)Y.uniform3fv(Z(a),U,c>>2,3*b);else{if(96>=b)for(var f=Nd[3*b-1],h=0;h<3*b;h+=3)f[h]=U[c+4*h>>2],f[h+1]=U[c+(4*h+4)>>2],f[h+2]=U[c+(4*h+8)>>2];else f=U.subarray(c>>
2,c+12*b>>2);Y.uniform3fv(Z(a),f)}},Uc:function(a,b,c,f){Y.uniform3i(Z(a),b,c,f)},Tc:function(a,b,c){if(2<=w.version)Y.uniform3iv(Z(a),P,c>>2,3*b);else{if(96>=b)for(var f=Od[3*b-1],h=0;h<3*b;h+=3)f[h]=P[c+4*h>>2],f[h+1]=P[c+(4*h+4)>>2],f[h+2]=P[c+(4*h+8)>>2];else f=P.subarray(c>>2,c+12*b>>2);Y.uniform3iv(Z(a),f)}},Sc:function(a,b,c,f,h){Y.uniform4f(Z(a),b,c,f,h)},Rc:function(a,b,c){if(2<=w.version)Y.uniform4fv(Z(a),U,c>>2,4*b);else{if(72>=b){var f=Nd[4*b-1],h=U;c>>=2;for(var m=0;m<4*b;m+=4){var t=
c+m;f[m]=h[t];f[m+1]=h[t+1];f[m+2]=h[t+2];f[m+3]=h[t+3]}}else f=U.subarray(c>>2,c+16*b>>2);Y.uniform4fv(Z(a),f)}},Fc:function(a,b,c,f,h){Y.uniform4i(Z(a),b,c,f,h)},Gc:function(a,b,c){if(2<=w.version)Y.uniform4iv(Z(a),P,c>>2,4*b);else{if(72>=b)for(var f=Od[4*b-1],h=0;h<4*b;h+=4)f[h]=P[c+4*h>>2],f[h+1]=P[c+(4*h+4)>>2],f[h+2]=P[c+(4*h+8)>>2],f[h+3]=P[c+(4*h+12)>>2];else f=P.subarray(c>>2,c+16*b>>2);Y.uniform4iv(Z(a),f)}},Hc:function(a,b,c,f){if(2<=w.version)Y.uniformMatrix2fv(Z(a),!!c,U,f>>2,4*b);else{if(72>=
b)for(var h=Nd[4*b-1],m=0;m<4*b;m+=4)h[m]=U[f+4*m>>2],h[m+1]=U[f+(4*m+4)>>2],h[m+2]=U[f+(4*m+8)>>2],h[m+3]=U[f+(4*m+12)>>2];else h=U.subarray(f>>2,f+16*b>>2);Y.uniformMatrix2fv(Z(a),!!c,h)}},Ic:function(a,b,c,f){if(2<=w.version)Y.uniformMatrix3fv(Z(a),!!c,U,f>>2,9*b);else{if(32>=b)for(var h=Nd[9*b-1],m=0;m<9*b;m+=9)h[m]=U[f+4*m>>2],h[m+1]=U[f+(4*m+4)>>2],h[m+2]=U[f+(4*m+8)>>2],h[m+3]=U[f+(4*m+12)>>2],h[m+4]=U[f+(4*m+16)>>2],h[m+5]=U[f+(4*m+20)>>2],h[m+6]=U[f+(4*m+24)>>2],h[m+7]=U[f+(4*m+28)>>2],h[m+
8]=U[f+(4*m+32)>>2];else h=U.subarray(f>>2,f+36*b>>2);Y.uniformMatrix3fv(Z(a),!!c,h)}},Jc:function(a,b,c,f){if(2<=w.version)Y.uniformMatrix4fv(Z(a),!!c,U,f>>2,16*b);else{if(18>=b){var h=Nd[16*b-1],m=U;f>>=2;for(var t=0;t<16*b;t+=16){var n=f+t;h[t]=m[n];h[t+1]=m[n+1];h[t+2]=m[n+2];h[t+3]=m[n+3];h[t+4]=m[n+4];h[t+5]=m[n+5];h[t+6]=m[n+6];h[t+7]=m[n+7];h[t+8]=m[n+8];h[t+9]=m[n+9];h[t+10]=m[n+10];h[t+11]=m[n+11];h[t+12]=m[n+12];h[t+13]=m[n+13];h[t+14]=m[n+14];h[t+15]=m[n+15]}}else h=U.subarray(f>>2,f+
64*b>>2);Y.uniformMatrix4fv(Z(a),!!c,h)}},Kc:function(a){a=pd[a];Y.useProgram(a);Y.If=a},Lc:function(a,b){Y.vertexAttrib1f(a,b)},Mc:function(a,b){Y.vertexAttrib2f(a,U[b>>2],U[b+4>>2])},Nc:function(a,b){Y.vertexAttrib3f(a,U[b>>2],U[b+4>>2],U[b+8>>2])},Oc:function(a,b){Y.vertexAttrib4f(a,U[b>>2],U[b+4>>2],U[b+8>>2],U[b+12>>2])},pc:function(a,b){Y.vertexAttribDivisor(a,b)},qc:function(a,b,c,f,h){Y.vertexAttribIPointer(a,b,c,f,h)},Pc:function(a,b,c,f,h,m){Y.vertexAttribPointer(a,b,c,!!f,h,m)},Qc:function(a,
b,c,f){Y.viewport(a,b,c,f)},lb:function(a,b,c,f){Y.waitSync(vd[a],b,(c>>>0)+4294967296*f)},tb:function(a){var b=J.length;a>>>=0;if(2147483648<a)return!1;for(var c=1;4>=c;c*=2){var f=b*(1+.2/c);f=Math.min(f,a+100663296);var h=Math;f=Math.max(a,f);h=h.min.call(h,2147483648,f+(65536-f%65536)%65536);a:{try{Qa.grow(h-kb.byteLength+65535>>>16);ob();var m=1;break a}catch(t){}m=void 0}if(m)return!0}return!1},nb:function(){return w?w.Xf:0},wb:function(a,b){var c=0;Qd().forEach(function(f,h){var m=b+c;h=P[a+
4*h>>2]=m;for(m=0;m<f.length;++m)lb[h++>>0]=f.charCodeAt(m);lb[h>>0]=0;c+=f.length+1});return 0},xb:function(a,b){var c=Qd();P[a>>2]=c.length;var f=0;c.forEach(function(h){f+=h.length+1});P[b>>2]=f;return 0},Jb:function(a){if(!noExitRuntime){if(v.onExit)v.onExit(a);Ra=!0}wa(a,new Ja(a))},P:function(){return 0},ob:function(a,b,c,f,h,m){a=Gb.Tf(a);b=Gb.Kf(a,b,c,f);P[m>>2]=b;return 0},Cb:function(a,b,c,f){a=Gb.Tf(a);b=Gb.Kf(a,b,c);P[f>>2]=b;return 0},pb:function(){},U:function(a,b,c,f){for(var h=0,m=
0;m<c;m++){var t=P[b>>2],n=P[b+4>>2];b+=8;for(var q=0;q<n;q++){var x=J[t+q],C=Fb[a];0===x||10===x?((1===a?La:Ka)(Ua(C,0)),C.length=0):C.push(x)}h+=n}P[f>>2]=h;return 0},c:function(){return Ma},m:be,s:ce,k:de,J:ee,Nb:fe,$:ge,_:he,R:ie,q:je,x:ke,n:le,v:me,Mb:ne,Kb:oe,Lb:pe,d:function(a){Ma=a},rb:function(a,b,c,f){return Vd(a,b,c,f)}};
(function(){function a(h){v.asm=h.exports;Qa=v.asm.dd;ob();pb=v.asm.fd;rb.unshift(v.asm.ed);ub--;v.monitorRunDependencies&&v.monitorRunDependencies(ub);0==ub&&(null!==vb&&(clearInterval(vb),vb=null),wb&&(h=wb,wb=null,h()))}function b(h){a(h.instance)}function c(h){return Cb().then(function(m){return WebAssembly.instantiate(m,f)}).then(function(m){return m}).then(h,function(m){Ka("failed to asynchronously prepare wasm: "+m);Pa(m)})}var f={a:qe};ub++;v.monitorRunDependencies&&v.monitorRunDependencies(ub);
if(v.instantiateWasm)try{return v.instantiateWasm(f,a)}catch(h){return Ka("Module.instantiateWasm callback failed with error: "+h),!1}(function(){return Na||"function"!=typeof WebAssembly.instantiateStreaming||yb()||zb.startsWith("file://")||"function"!=typeof fetch?c(b):fetch(zb,{credentials:"same-origin"}).then(function(h){return WebAssembly.instantiateStreaming(h,f).then(b,function(m){Ka("wasm streaming compile failed: "+m);Ka("falling back to ArrayBuffer instantiation");return c(b)})})})().catch(fa);
return{}})();v.___wasm_call_ctors=function(){return(v.___wasm_call_ctors=v.asm.ed).apply(null,arguments)};var Oc=v._free=function(){return(Oc=v._free=v.asm.gd).apply(null,arguments)},Jd=v._malloc=function(){return(Jd=v._malloc=v.asm.hd).apply(null,arguments)},Nc=v.___getTypeName=function(){return(Nc=v.___getTypeName=v.asm.id).apply(null,arguments)};v.___embind_register_native_and_builtin_types=function(){return(v.___embind_register_native_and_builtin_types=v.asm.jd).apply(null,arguments)};
var re=v._setThrew=function(){return(re=v._setThrew=v.asm.kd).apply(null,arguments)},se=v.stackSave=function(){return(se=v.stackSave=v.asm.ld).apply(null,arguments)},te=v.stackRestore=function(){return(te=v.stackRestore=v.asm.md).apply(null,arguments)};v.dynCall_viji=function(){return(v.dynCall_viji=v.asm.nd).apply(null,arguments)};v.dynCall_vijiii=function(){return(v.dynCall_vijiii=v.asm.od).apply(null,arguments)};v.dynCall_viiiiij=function(){return(v.dynCall_viiiiij=v.asm.pd).apply(null,arguments)};
v.dynCall_jiiiijiiiii=function(){return(v.dynCall_jiiiijiiiii=v.asm.qd).apply(null,arguments)};v.dynCall_viiij=function(){return(v.dynCall_viiij=v.asm.rd).apply(null,arguments)};v.dynCall_jii=function(){return(v.dynCall_jii=v.asm.sd).apply(null,arguments)};v.dynCall_vij=function(){return(v.dynCall_vij=v.asm.td).apply(null,arguments)};v.dynCall_iiij=function(){return(v.dynCall_iiij=v.asm.ud).apply(null,arguments)};v.dynCall_iiiij=function(){return(v.dynCall_iiiij=v.asm.vd).apply(null,arguments)};
v.dynCall_viij=function(){return(v.dynCall_viij=v.asm.wd).apply(null,arguments)};v.dynCall_ji=function(){return(v.dynCall_ji=v.asm.xd).apply(null,arguments)};v.dynCall_iij=function(){return(v.dynCall_iij=v.asm.yd).apply(null,arguments)};v.dynCall_jiiii=function(){return(v.dynCall_jiiii=v.asm.zd).apply(null,arguments)};v.dynCall_jiiiiii=function(){return(v.dynCall_jiiiiii=v.asm.Ad).apply(null,arguments)};v.dynCall_jiiiiji=function(){return(v.dynCall_jiiiiji=v.asm.Bd).apply(null,arguments)};
v.dynCall_iijj=function(){return(v.dynCall_iijj=v.asm.Cd).apply(null,arguments)};v.dynCall_iiiji=function(){return(v.dynCall_iiiji=v.asm.Dd).apply(null,arguments)};v.dynCall_iiji=function(){return(v.dynCall_iiji=v.asm.Ed).apply(null,arguments)};v.dynCall_iijjiii=function(){return(v.dynCall_iijjiii=v.asm.Fd).apply(null,arguments)};v.dynCall_vijjjii=function(){return(v.dynCall_vijjjii=v.asm.Gd).apply(null,arguments)};v.dynCall_jiji=function(){return(v.dynCall_jiji=v.asm.Hd).apply(null,arguments)};
v.dynCall_viijii=function(){return(v.dynCall_viijii=v.asm.Id).apply(null,arguments)};v.dynCall_iiiiij=function(){return(v.dynCall_iiiiij=v.asm.Jd).apply(null,arguments)};v.dynCall_iiiiijj=function(){return(v.dynCall_iiiiijj=v.asm.Kd).apply(null,arguments)};v.dynCall_iiiiiijj=function(){return(v.dynCall_iiiiiijj=v.asm.Ld).apply(null,arguments)};function be(a,b){var c=se();try{return Eb(a)(b)}catch(f){te(c);if(f!==f+0)throw f;re(1,0)}}
function ce(a,b,c){var f=se();try{return Eb(a)(b,c)}catch(h){te(f);if(h!==h+0)throw h;re(1,0)}}function le(a,b,c,f){var h=se();try{Eb(a)(b,c,f)}catch(m){te(h);if(m!==m+0)throw m;re(1,0)}}function de(a,b,c,f){var h=se();try{return Eb(a)(b,c,f)}catch(m){te(h);if(m!==m+0)throw m;re(1,0)}}function je(a,b){var c=se();try{Eb(a)(b)}catch(f){te(c);if(f!==f+0)throw f;re(1,0)}}function ke(a,b,c){var f=se();try{Eb(a)(b,c)}catch(h){te(f);if(h!==h+0)throw h;re(1,0)}}
function fe(a,b,c,f,h,m){var t=se();try{return Eb(a)(b,c,f,h,m)}catch(n){te(t);if(n!==n+0)throw n;re(1,0)}}function me(a,b,c,f,h){var m=se();try{Eb(a)(b,c,f,h)}catch(t){te(m);if(t!==t+0)throw t;re(1,0)}}function ge(a,b,c,f,h,m,t){var n=se();try{return Eb(a)(b,c,f,h,m,t)}catch(q){te(n);if(q!==q+0)throw q;re(1,0)}}function ee(a,b,c,f,h){var m=se();try{return Eb(a)(b,c,f,h)}catch(t){te(m);if(t!==t+0)throw t;re(1,0)}}
function ne(a,b,c,f,h,m){var t=se();try{Eb(a)(b,c,f,h,m)}catch(n){te(t);if(n!==n+0)throw n;re(1,0)}}function pe(a,b,c,f,h,m,t,n,q,x){var C=se();try{Eb(a)(b,c,f,h,m,t,n,q,x)}catch(H){te(C);if(H!==H+0)throw H;re(1,0)}}function ie(a){var b=se();try{Eb(a)()}catch(c){te(b);if(c!==c+0)throw c;re(1,0)}}function oe(a,b,c,f,h,m,t){var n=se();try{Eb(a)(b,c,f,h,m,t)}catch(q){te(n);if(q!==q+0)throw q;re(1,0)}}
function he(a,b,c,f,h,m,t,n,q,x){var C=se();try{return Eb(a)(b,c,f,h,m,t,n,q,x)}catch(H){te(C);if(H!==H+0)throw H;re(1,0)}}var ue;function Ja(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}wb=function ve(){ue||we();ue||(wb=ve)};
function we(){function a(){if(!ue&&(ue=!0,v.calledRun=!0,!Ra)){Db(rb);da(v);if(v.onRuntimeInitialized)v.onRuntimeInitialized();if(v.postRun)for("function"==typeof v.postRun&&(v.postRun=[v.postRun]);v.postRun.length;){var b=v.postRun.shift();sb.unshift(b)}Db(sb)}}if(!(0<ub)){if(v.preRun)for("function"==typeof v.preRun&&(v.preRun=[v.preRun]);v.preRun.length;)tb();Db(qb);0<ub||(v.setStatus?(v.setStatus("Running..."),setTimeout(function(){setTimeout(function(){v.setStatus("")},1);a()},1)):a())}}
v.run=we;if(v.preInit)for("function"==typeof v.preInit&&(v.preInit=[v.preInit]);0<v.preInit.length;)v.preInit.pop()();we();


  return CanvasKitInit.ready
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = CanvasKitInit;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return CanvasKitInit; });
else if (typeof exports === 'object')
  exports["CanvasKitInit"] = CanvasKitInit;
