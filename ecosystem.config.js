module.exports = {
  apps: [
    {
      name: "QURANI_TUFUKU_API",
      script: "app.js",
      env: {
        NODE_ENV: "development",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
  ],

  deploy: {
    prod: {
      user: "root",
      host: "**************",
      ref: "origin/develop",
      repo: "**************:minhtruong315/qurani_tukufu_server.git",
      path: "/root/qurani_tukufu_prod",
      "pre-deploy": "git add . && git reset --hard",
      "post-deploy":
        "yarn install && pm2 startOrRestart ecosystem.config.js --env production",
    },
    dev: {
      user: "root",
      host: "**************",
      ref: "origin/develop",
      repo: "**************:minhtruong315/qurani_tukufu_server.git",
      path: "/root/qurani_tukufu_dev",
      "pre-deploy": "git add . && git reset --hard",
      "post-deploy":
        "yarn install && pm2 startOrRestart ecosystem.config.js",
    },
  },
};
